/**
 * useWorkoutDetail Hook
 * 
 * @fileoverview WorkoutDetail页面的核心数据获取和状态管理Hook
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { UIWorkoutDetail } from '../../../models/ui/workout';
import { WorkoutDetailTransformer } from '../../../models/transformers/workout/WorkoutDetailTransformer';
import { communityService } from '../../../services/communityService';
import { workoutService } from '../../../services/workout';
import { useWorkoutDetailCache } from './useWorkoutDetailCache';
import { useWorkoutDataStrategy } from './useWorkoutDataStrategy';
import { usePerformanceMonitor, debounceExecutor } from '../utils/performanceMonitor';

import { DataSource, SocialAction } from '../types';

export interface UseWorkoutDetailResult {
  workoutDetail: UIWorkoutDetail | null;
  loading: boolean;
  error: string | null;
  
  // 交互状态
  expandedExercises: string[];
  
  // 交互方法
  toggleExerciseExpansion: (exerciseId: string) => void;
  handleSocialAction: (action: SocialAction) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * WorkoutDetail 数据获取和状态管理 Hook
 * 
 * @param dataSource 数据源配置
 * @param initialData 初始数据（可选，用于避免重复请求）
 * @returns Hook 结果对象
 */
export const useWorkoutDetail = (
  dataSource: DataSource,
  initialData?: any
): UseWorkoutDetailResult => {
  const [workoutDetail, setWorkoutDetail] = useState<UIWorkoutDetail | null>(null);
  const [loading, setLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [expandedExercises, setExpandedExercises] = useState<string[]>([]);

  // 添加循环检测
  const executionCountRef = useRef(0);
  const lastProcessedDataRef = useRef<string | null>(null);
  const isProcessingRef = useRef(false);

  // 性能监控
  const performanceMonitor = usePerformanceMonitor('useWorkoutDetail');

  // 初始化缓存 - 使用 useMemo 稳定化配置对象
  const cacheConfig = useMemo(() => ({
    ttl: 5 * 60 * 1000, // 5分钟缓存
    maxEntries: 50
  }), []);

  const cache = useWorkoutDetailCache(cacheConfig);

  // 初始化数据获取策略 - 使用 useMemo 稳定化配置对象
  const dataStrategyConfig = useMemo(() => ({
    enableFallback: true,
    timeout: 10000
  }), []);

  const dataStrategy = useWorkoutDataStrategy(dataStrategyConfig);



  // 数据获取函数
  const fetchWorkoutDetail = useCallback(async () => {
    const endMonitoring = performanceMonitor.startOperation();

    // 检测循环执行
    const loopDetection = performanceMonitor.detectLoop(5000, 5);
    if (loopDetection.isLooping) {
      console.error('[useWorkoutDetail] 检测到循环执行，停止处理');
      endMonitoring();
      return;
    }

    // 防止重复处理
    if (isProcessingRef.current) {
      console.log('[useWorkoutDetail] 数据正在处理中，跳过重复请求');
      endMonitoring();
      return;
    }

    const dataKey = `${dataSource.type}:${dataSource.id}`;
    if (lastProcessedDataRef.current === dataKey) {
      console.log('[useWorkoutDetail] 相同数据已处理，跳过重复请求');
      endMonitoring();
      return;
    }

    isProcessingRef.current = true;
    executionCountRef.current += 1;

    console.log(`[useWorkoutDetail] 开始数据获取 (执行次数: ${executionCountRef.current})`);

    try {
      setLoading(true);
      setError(null);

      // 首先检查缓存
      const cachedData = cache.get(dataSource.id, dataSource.type);
      if (cachedData) {
        console.log('[useWorkoutDetail] 缓存命中，使用缓存数据');
        setWorkoutDetail(cachedData);
        lastProcessedDataRef.current = dataKey;
        setLoading(false);
        return;
      }

      // 使用智能数据获取策略
      const result = await dataStrategy.fetchWorkoutDetail(dataSource);

      // 缓存结果
      cache.set(dataSource.id, dataSource.type, result);
      setWorkoutDetail(result);
      lastProcessedDataRef.current = dataKey;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('获取训练详情失败:', err);
    } finally {
      setLoading(false);
      isProcessingRef.current = false;
      endMonitoring();
    }
  }, [dataSource.type, dataSource.id, cache, dataStrategy, performanceMonitor]);

  // 稳定化 dataSource 对象引用
  const stableDataSource = useMemo(() => dataSource, [dataSource.type, dataSource.id]);

  // 稳定化 initialData 引用（只在真正变化时更新）
  const stableInitialData = useMemo(() => {
    if (!initialData) return null;
    // 使用 JSON.stringify 来比较对象内容而不是引用
    return initialData;
  }, [initialData ? JSON.stringify(initialData) : null]);

  // 初始化数据获取
  useEffect(() => {
    // 防止重复执行
    const dataKey = `${stableDataSource.type}:${stableDataSource.id}`;
    if (isProcessingRef.current || lastProcessedDataRef.current === dataKey) {
      console.log('[useWorkoutDetail] 跳过重复的初始化处理');
      return;
    }

    if (stableInitialData) {
      // 使用初始数据，避免重复网络请求
      console.log('[useWorkoutDetail] 使用初始数据:', {
        dataSourceType: stableDataSource.type,
        dataKey,
        executionCount: executionCountRef.current + 1
      });

      // 创建异步函数来处理初始数据
      const processInitialData = async () => {
        if (isProcessingRef.current) {
          console.log('[useWorkoutDetail] 初始数据处理中，跳过');
          return;
        }

        isProcessingRef.current = true;
        executionCountRef.current += 1;

        try {
          let result: UIWorkoutDetail;

          // 使用智能数据获取策略处理初始数据
          result = await dataStrategy.fetchWorkoutDetail(stableDataSource, stableInitialData);

          // 缓存初始数据转换结果
          cache.set(stableDataSource.id, stableDataSource.type, result);
          setWorkoutDetail(result);
          setLoading(false);
          lastProcessedDataRef.current = dataKey;
          console.log('[useWorkoutDetail] 初始数据转换成功:', result);

        } catch (err) {
          console.error('[useWorkoutDetail] 初始数据转换失败:', err);
          // 降级到网络请求
          fetchWorkoutDetail();
        } finally {
          isProcessingRef.current = false;
        }
      };

      // 立即执行异步函数
      processInitialData();
    } else {
      console.log('[useWorkoutDetail] 无初始数据，开始网络请求');
      fetchWorkoutDetail();
    }
  }, [stableDataSource.type, stableDataSource.id, stableInitialData, fetchWorkoutDetail, dataStrategy, cache]);

  // 动作展开/折叠
  const toggleExerciseExpansion = useCallback((exerciseId: string) => {
    console.log('[useWorkoutDetail] toggleExerciseExpansion 被调用:', {
      exerciseId,
      currentExpandedExercises: expandedExercises,
      isCurrentlyExpanded: expandedExercises.includes(exerciseId)
    });

    setExpandedExercises(prev => {
      const newExpanded = prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId];

      console.log('[useWorkoutDetail] expandedExercises 状态更新:', {
        from: prev,
        to: newExpanded,
        exerciseId
      });

      return newExpanded;
    });
  }, [expandedExercises]);

  // 社交操作处理
  const handleSocialAction = useCallback(async (action: SocialAction) => {
    if (!workoutDetail?.socialInfo) {
      console.warn('当前页面不支持社交操作');
      return;
    }
    
    try {
      switch (action.type) {
        case 'like':
          // 使用社区服务的点赞功能
          await communityService.togglePostLike(workoutDetail.socialInfo.post.id);
          
          // 乐观更新UI
          setWorkoutDetail(prev => prev ? {
            ...prev,
            socialInfo: {
              ...prev.socialInfo!,
              stats: {
                ...prev.socialInfo!.stats,
                isLiked: !prev.socialInfo!.stats.isLiked,
                likes: prev.socialInfo!.stats.isLiked 
                  ? prev.socialInfo!.stats.likes - 1
                  : prev.socialInfo!.stats.likes + 1
              }
            }
          } : null);
          break;
          
        case 'follow':
          // TODO: 实现关注逻辑
          console.log('关注功能待实现');
          break;
          
        case 'share':
          // TODO: 实现分享逻辑
          console.log('分享功能待实现');
          break;
          
        case 'comment':
          // TODO: 实现评论逻辑
          console.log('评论功能待实现');
          break;
          
        default:
          console.warn('未知的社交操作类型:', action.type);
      }
    } catch (err) {
      console.error('社交操作失败:', err);
      // TODO: 显示错误提示给用户
      // 如果是乐观更新，这里应该回滚状态
    }
  }, [workoutDetail]);

  // 刷新数据
  const refreshData = useCallback(async () => {
    await fetchWorkoutDetail();
  }, [fetchWorkoutDetail]);

  // 返回 Hook 结果
  return useMemo(() => ({
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  }), [
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  ]);
};