/**
 * useWorkoutDetailCache Hook
 * 
 * @fileoverview 训练详情数据缓存管理Hook
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { useState, useCallback, useRef, useMemo } from 'react';
import { UIWorkoutDetail } from '../../../models/ui/workout';

interface CacheEntry {
  data: UIWorkoutDetail;
  timestamp: number;
  source: 'feed' | 'profile';
}

interface WorkoutDetailCacheConfig {
  /** 缓存过期时间（毫秒），默认5分钟 */
  ttl: number;
  /** 最大缓存条目数，默认50 */
  maxEntries: number;
}

const DEFAULT_CONFIG: WorkoutDetailCacheConfig = {
  ttl: 5 * 60 * 1000, // 5分钟
  maxEntries: 50
};

/**
 * 训练详情数据缓存Hook
 * 
 * @param config 缓存配置
 * @returns 缓存操作方法
 */
export const useWorkoutDetailCache = (config: Partial<WorkoutDetailCacheConfig> = {}) => {
  // 使用 useMemo 稳定化配置对象
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config.ttl, config.maxEntries]);
  const cacheRef = useRef<Map<string, CacheEntry>>(new Map());
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    entries: 0
  });

  /**
   * 生成缓存键
   */
  const generateCacheKey = useCallback((id: string, source: 'feed' | 'profile'): string => {
    return `${source}:${id}`;
  }, []);

  /**
   * 检查缓存条目是否过期
   */
  const isExpired = useCallback((entry: CacheEntry): boolean => {
    return Date.now() - entry.timestamp > finalConfig.ttl;
  }, [finalConfig.ttl]);

  /**
   * 清理过期的缓存条目
   */
  const cleanupExpiredEntries = useCallback(() => {
    const cache = cacheRef.current;
    const expiredKeys: string[] = [];
    
    cache.forEach((entry, key) => {
      if (isExpired(entry)) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => cache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`[WorkoutDetailCache] 清理了 ${expiredKeys.length} 个过期缓存条目`);
      setCacheStats(prev => ({ ...prev, entries: cache.size }));
    }
  }, [isExpired]);

  /**
   * 限制缓存大小
   */
  const limitCacheSize = useCallback(() => {
    const cache = cacheRef.current;
    
    if (cache.size > finalConfig.maxEntries) {
      // 按时间戳排序，删除最旧的条目
      const entries = Array.from(cache.entries()).sort((a, b) => a[1].timestamp - b[1].timestamp);
      const toDelete = entries.slice(0, cache.size - finalConfig.maxEntries);
      
      toDelete.forEach(([key]) => cache.delete(key));
      
      console.log(`[WorkoutDetailCache] 删除了 ${toDelete.length} 个最旧的缓存条目`);
      setCacheStats(prev => ({ ...prev, entries: cache.size }));
    }
  }, [finalConfig.maxEntries]);

  /**
   * 获取缓存数据
   */
  const get = useCallback((id: string, source: 'feed' | 'profile'): UIWorkoutDetail | null => {
    const key = generateCacheKey(id, source);
    const entry = cacheRef.current.get(key);
    
    if (!entry) {
      setCacheStats(prev => ({ ...prev, misses: prev.misses + 1 }));
      return null;
    }
    
    if (isExpired(entry)) {
      cacheRef.current.delete(key);
      setCacheStats(prev => ({ 
        ...prev, 
        misses: prev.misses + 1,
        entries: prev.entries - 1
      }));
      return null;
    }
    
    setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
    console.log(`[WorkoutDetailCache] 缓存命中: ${key}`);
    return entry.data;
  }, [generateCacheKey, isExpired]);

  /**
   * 设置缓存数据
   */
  const set = useCallback((id: string, source: 'feed' | 'profile', data: UIWorkoutDetail) => {
    const key = generateCacheKey(id, source);
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      source
    };
    
    cacheRef.current.set(key, entry);
    console.log(`[WorkoutDetailCache] 缓存设置: ${key}`);
    
    // 清理过期条目和限制缓存大小
    cleanupExpiredEntries();
    limitCacheSize();
    
    setCacheStats(prev => ({ ...prev, entries: cacheRef.current.size }));
  }, [generateCacheKey, cleanupExpiredEntries, limitCacheSize]);

  /**
   * 删除特定缓存条目
   */
  const remove = useCallback((id: string, source: 'feed' | 'profile') => {
    const key = generateCacheKey(id, source);
    const deleted = cacheRef.current.delete(key);
    
    if (deleted) {
      console.log(`[WorkoutDetailCache] 缓存删除: ${key}`);
      setCacheStats(prev => ({ ...prev, entries: prev.entries - 1 }));
    }
    
    return deleted;
  }, [generateCacheKey]);

  /**
   * 清空所有缓存
   */
  const clear = useCallback(() => {
    const size = cacheRef.current.size;
    cacheRef.current.clear();
    console.log(`[WorkoutDetailCache] 清空所有缓存，共 ${size} 个条目`);
    setCacheStats({ hits: 0, misses: 0, entries: 0 });
  }, []);

  /**
   * 获取缓存统计信息
   */
  const getStats = useCallback(() => {
    const hitRate = cacheStats.hits + cacheStats.misses > 0 
      ? (cacheStats.hits / (cacheStats.hits + cacheStats.misses) * 100).toFixed(2)
      : '0.00';
    
    return {
      ...cacheStats,
      hitRate: `${hitRate}%`,
      size: cacheRef.current.size
    };
  }, [cacheStats]);

  return {
    get,
    set,
    remove,
    clear,
    getStats,
    cleanupExpiredEntries
  };
};
