/**
 * useWorkoutDataStrategy Hook
 * 
 * @fileoverview 智能的训练数据获取策略Hook，实现数据获取的降级机制
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { useCallback, useMemo } from 'react';
import { UIWorkoutDetail } from '../../../models/ui/workout';
import { WorkoutDetailTransformer } from '../../../models/transformers/workout/WorkoutDetailTransformer';
import { communityService } from '../../../services/communityService';
import { workoutService } from '../../../services/workout';
import { DataSource } from '../types';
import {
  detectDataFormat,
  hasCompleteWorkoutData,
  extractWorkoutId as extractWorkoutIdUtil,
  getDataFormatDebugInfo
} from '../utils/dataFormatDetector';

interface WorkoutDataStrategyOptions {
  /** 是否启用降级策略 */
  enableFallback: boolean;
  /** 超时时间（毫秒） */
  timeout: number;
}

const DEFAULT_OPTIONS: WorkoutDataStrategyOptions = {
  enableFallback: true,
  timeout: 10000 // 10秒
};

/**
 * 智能训练数据获取策略Hook
 * 
 * @param options 配置选项
 * @returns 数据获取方法
 */
export const useWorkoutDataStrategy = (options: Partial<WorkoutDataStrategyOptions> = {}) => {
  // 使用 useMemo 稳定化配置对象
  const finalOptions = useMemo(() => ({ ...DEFAULT_OPTIONS, ...options }), [options.enableFallback, options.timeout]);

  /**
   * 检查Feed数据是否包含完整的训练详情
   * 使用统一的数据格式检测工具
   */
  const hasCompleteWorkoutDetail = useCallback((feedData: any): boolean => {
    return hasCompleteWorkoutData(feedData);
  }, []);

  /**
   * 从Feed数据中提取训练ID
   * 使用统一的数据格式检测工具
   */
  const extractWorkoutId = useCallback((feedData: any): string | null => {
    return extractWorkoutIdUtil(feedData);
  }, []);

  /**
   * Feed场景的数据获取策略
   */
  const fetchFeedWorkoutDetail = useCallback(async (
    dataSource: DataSource,
    initialData?: any
  ): Promise<UIWorkoutDetail> => {
    const detection = detectDataFormat(initialData);

    console.log('[WorkoutDataStrategy] Feed场景数据获取开始');
    console.log('[WorkoutDataStrategy] 数据格式检测结果:');
    console.log(getDataFormatDebugInfo(initialData));

    // 策略1: 如果有完整的初始数据，直接使用
    if (initialData && hasCompleteWorkoutDetail(initialData)) {
      console.log('[WorkoutDataStrategy] 策略1: 使用完整的初始数据');

      // 根据检测到的格式进行处理
      switch (detection.format) {
        case 'ui_feed_post':
          console.log('[WorkoutDataStrategy] 检测到UI FeedPost格式，直接转换');
          return WorkoutDetailTransformer.transformPostDetailToUIModel(initialData);

        case 'api_feed_post':
          console.log('[WorkoutDataStrategy] 检测到API FeedPost格式，需要先转换为UI格式');
          // 通过communityService获取转换后的数据
          const feedPost = await communityService.getPostDetail(dataSource.id);
          return WorkoutDetailTransformer.transformPostDetailToUIModel(feedPost);

        default:
          console.warn('[WorkoutDataStrategy] 未知的数据格式，降级到网络请求');
          break;
      }
    }

    // 策略2: 通过postId获取完整的Feed数据
    try {
      console.log('[WorkoutDataStrategy] 策略2: 通过postId获取Feed数据');
      const feedPost = await communityService.getPostDetail(dataSource.id);
      
      if (hasCompleteWorkoutDetail(feedPost)) {
        console.log('[WorkoutDataStrategy] Feed数据包含完整训练详情');
        return WorkoutDetailTransformer.transformPostDetailToUIModel(feedPost);
      }

      // 策略3: Feed数据不完整，尝试通过workoutId获取训练详情
      if (finalOptions.enableFallback) {
        const workoutId = extractWorkoutId(feedPost);
        if (workoutId) {
          console.log('[WorkoutDataStrategy] 策略3: 通过workoutId获取训练详情', { workoutId });
          
          // 确保认证
          const { authService } = await import('../../../services/authService');
          if (!authService.isAuthenticated()) {
            await authService.loginWithTestUser();
          }

          const workoutResponse = await workoutService.getWorkoutDetail(workoutId);
          if (workoutResponse.success && workoutResponse.data) {
            // 合并Feed的社交信息和训练详情
            const workoutDetail = WorkoutDetailTransformer.transformWorkoutResponseToUIModel(workoutResponse.data);
            
            // 添加社交信息
            if (feedPost.user && feedPost.content) {
              workoutDetail.socialInfo = {
                user: {
                  id: feedPost.user.id,
                  name: feedPost.user.name,
                  avatar: feedPost.user.avatar,
                  isFollowing: feedPost.user.isFollowing || false,
                  isVerified: feedPost.user.isVerified || false
                },
                post: {
                  id: feedPost.id,
                  content: feedPost.content.text || '',
                  timestamp: feedPost.timestamp.toISOString(),
                  images: feedPost.images || []
                },
                stats: {
                  likes: feedPost.stats.likes,
                  comments: feedPost.stats.comments,
                  views: feedPost.stats.views || 0,
                  isLiked: feedPost.isLiked
                }
              };
            }
            
            return workoutDetail;
          }
        }
      }

      throw new Error('无法获取完整的训练数据');

    } catch (error) {
      console.error('[WorkoutDataStrategy] Feed数据获取失败:', error);
      throw error;
    }
  }, [hasCompleteWorkoutDetail, extractWorkoutId, finalOptions.enableFallback]);

  /**
   * Profile场景的数据获取策略
   */
  const fetchProfileWorkoutDetail = useCallback(async (
    dataSource: DataSource,
    initialData?: any
  ): Promise<UIWorkoutDetail> => {
    console.log('[WorkoutDataStrategy] Profile场景数据获取开始');

    // 策略1: 如果有初始数据，直接使用
    if (initialData) {
      console.log('[WorkoutDataStrategy] 使用初始数据');
      return WorkoutDetailTransformer.transformWorkoutResponseToUIModel(initialData);
    }

    // 策略2: 通过workoutId获取训练详情
    try {
      console.log('[WorkoutDataStrategy] 通过workoutId获取训练详情');
      
      // 确保认证
      const { authService } = await import('../../../services/authService');
      if (!authService.isAuthenticated()) {
        await authService.loginWithTestUser();
      }

      const response = await workoutService.getWorkoutDetail(dataSource.id);
      
      if (response.success && response.data) {
        return WorkoutDetailTransformer.transformWorkoutResponseToUIModel(response.data);
      } else {
        throw new Error(response.error?.message || '获取训练详情失败');
      }

    } catch (error) {
      console.error('[WorkoutDataStrategy] Profile数据获取失败:', error);
      throw error;
    }
  }, []);

  /**
   * 统一的数据获取方法
   */
  const fetchWorkoutDetail = useCallback(async (
    dataSource: DataSource,
    initialData?: any
  ): Promise<UIWorkoutDetail> => {
    const startTime = Date.now();
    
    try {
      let result: UIWorkoutDetail;

      if (dataSource.type === 'feed') {
        result = await fetchFeedWorkoutDetail(dataSource, initialData);
      } else {
        result = await fetchProfileWorkoutDetail(dataSource, initialData);
      }

      const duration = Date.now() - startTime;
      console.log(`[WorkoutDataStrategy] 数据获取完成，耗时: ${duration}ms`);
      
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[WorkoutDataStrategy] 数据获取失败，耗时: ${duration}ms`, error);
      throw error;
    }
  }, [fetchFeedWorkoutDetail, fetchProfileWorkoutDetail]);

  return {
    fetchWorkoutDetail,
    hasCompleteWorkoutDetail,
    extractWorkoutId
  };
};
