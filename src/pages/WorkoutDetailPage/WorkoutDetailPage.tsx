/**
 * WorkoutDetailPage 主页面组件
 * 
 * @fileoverview 训练详情页面的主要组件
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useMemo } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useWorkoutDetail } from './hooks/useWorkoutDetail';
import { DataSource } from './types';
import { TrainingExerciseCard } from './components/TrainingExerciseCard';
import { SocialSection } from './components/SocialSection';
import { WorkoutDetailSkeleton } from './components/WorkoutDetailSkeleton';
import { ErrorStateComponent, NotFoundState } from './components/ErrorStates';
import { MuscleInfoCard } from '../../components/fitness/MuscleVisualization/MuscleInfoCard';
import { WorkoutStatsCard } from '../../components/fitness/WorkoutStatsCard';
import { DropdownMenu } from '../../components/common/DropdownMenu';
import './WorkoutDetailPage.scss';

/**
 * WorkoutDetailPage 组件属性
 */
export interface WorkoutDetailPageProps {
  className?: string;
}

/**
 * 训练详情页面主组件
 * 支持两种业务场景：Feed社交场景和Profile纯数据场景
 */
const WorkoutDetailPage: React.FC<WorkoutDetailPageProps> = ({
  className = ''
}) => {
  const { postId, workoutId } = useParams<{ postId?: string; workoutId?: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  // 确定数据源类型
  const dataSource: DataSource = useMemo(() => {
    console.log('[WorkoutDetailPage] 路由参数解析:', {
      postId,
      workoutId,
      pathname: location.pathname,
      hasInitialData: !!location.state?.initialData
    });

    // 优先检查是否有 initialData，如果有且包含 feed 相关数据，则为 feed 场景
    if (location.state?.initialData) {
      const initialData = location.state.initialData;
      console.log('[WorkoutDetailPage] 检测到初始数据:', initialData);

      // 检查是否是 FeedPost 格式（包含 user、content、stats 等字段）
      if (initialData.user && initialData.content && initialData.stats) {
        console.log('[WorkoutDetailPage] 初始数据为 FeedPost 格式，判断为 feed 场景');
        return { type: 'feed', id: postId || workoutId || initialData.id };
      }

      // 检查是否是 ApiWorkoutResponse 格式
      if (initialData.exercises && !initialData.user) {
        console.log('[WorkoutDetailPage] 初始数据为 ApiWorkoutResponse 格式，判断为 profile 场景');
        return { type: 'profile', id: workoutId || postId || initialData.id };
      }
    }

    // 根据路由路径和参数判断
    if (workoutId || location.pathname.includes('/detail/')) {
      console.log('[WorkoutDetailPage] 根据路径判断为 profile 场景');
      return { type: 'profile', id: workoutId || postId };
    }

    if (postId) {
      console.log('[WorkoutDetailPage] 根据 postId 判断为 feed 场景');
      return { type: 'feed', id: postId };
    }

    throw new Error('无效的路由参数：缺少 postId 或 workoutId');
  }, [postId, workoutId, location.pathname, location.state?.initialData]);

  // 使用核心 Hook 获取数据
  const {
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  } = useWorkoutDetail(dataSource, location.state?.initialData);

  // 返回按钮处理
  const handleGoBack = () => {
    navigate(-1);
  };

  // 分享处理
  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `${workoutDetail?.name || '训练详情'}`,
          text: '来看看我的训练记录！',
          url: window.location.href
        });
      } else {
        // 降级到复制链接
        await navigator.clipboard.writeText(window.location.href);
        console.log('训练链接已复制到剪贴板');
        // TODO: 显示成功提示
      }
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  // Dropdown菜单项处理
  const handleSaveAsTemplate = () => {
    try {
      console.log('保存为模板功能');
      // TODO: 实现保存为模板逻辑
    } catch (error) {
      console.error('保存模板失败:', error);
    }
  };

  const handleStartWorkout = () => {
    try {
      console.log('开始训练功能');
      // TODO: 实现开始训练逻辑
    } catch (error) {
      console.error('开始训练失败:', error);
    }
  };

  // Dropdown菜单配置
  const dropdownItems = [
    {
      key: 'save-template',
      label: '保存为模板',
      icon: '📋',
      onClick: handleSaveAsTemplate
    },
    {
      key: 'start-workout',
      label: '进行训练',
      icon: '💪',
      onClick: handleStartWorkout
    }
  ];

  // 加载状态
  if (loading) {
    return (
      <WorkoutDetailSkeleton 
        showSocialSection={dataSource.type === 'feed'}
        className={className}
      />
    );
  }

  // 错误状态
  if (error) {
    return (
      <ErrorStateComponent
        error={{ 
          code: 'NETWORK_ERROR', 
          message: error, 
          retryable: true 
        }}
        onRetry={refreshData}
        onGoBack={handleGoBack}
        className={className}
      />
    );
  }

  // 数据为空
  if (!workoutDetail) {
    return (
      <NotFoundState
        dataType={dataSource.type === 'feed' ? '训练帖子' : '训练记录'}
        onGoBack={handleGoBack}
        className={className}
      />
    );
  }

  return (
    <div className={`workout-detail-page ${className}`}>
      {/* 页面头部 */}
      <header className="page-header">
        <button className="back-button" onClick={handleGoBack} aria-label="返回">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="15,18 9,12 15,6"/>
          </svg>
        </button>
        <h1 className="page-title">训练</h1>
        <div className="header-actions">
          <button 
            className="share-button"
            onClick={handleShare}
            aria-label="分享训练"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 12V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V12"/>
              <polyline points="16,6 12,2 8,6"/>
              <line x1="12" y1="2" x2="12" y2="15"/>
            </svg>
          </button>
          <DropdownMenu
            trigger={
              <button className="more-button" aria-label="更多选项">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="1"/>
                  <circle cx="12" cy="5" r="1"/>
                  <circle cx="12" cy="19" r="1"/>
                </svg>
              </button>
            }
            items={dropdownItems}
            placement="bottom-end"
            className="header-dropdown"
          />
        </div>
      </header>

      {/* 内容区域 */}
      <main className="page-content">
        {/* 社交信息区域（仅Feed场景） */}
        <SocialSection 
          socialInfo={workoutDetail.socialInfo}
          onSocialAction={handleSocialAction}
        />

        {/* 训练统计 - 使用WorkoutStatsCard组件，与Feed页面保持一致 */}
        <section className="workout-stats-section">
          <WorkoutStatsCard
            workout={workoutDetail.workoutStats}
            compact={false}
            showDividers={true}
            stats={['duration', 'weight', 'calories']}
            clickable={false}
            className="feed-workout-stats"
          />
        </section>

        {/* 训练动作 */}
        <section className="exercises-section">
          <h2 className="section-title">动作列表</h2>
          <div className="exercises-list">
            {workoutDetail.exercises.map(exercise => {
              const isExpanded = expandedExercises.includes(exercise.id);
              console.log('[WorkoutDetailPage] 渲染动作卡片:', {
                exerciseId: exercise.id,
                exerciseName: exercise.name,
                isExpanded,
                expandedExercises,
                setRecordsCount: exercise.setRecords?.length || 0,
                setRecords: exercise.setRecords
              });

              return (
                <TrainingExerciseCard
                  key={exercise.id}
                  exercise={exercise}
                  isExpanded={isExpanded}
                  onToggleExpansion={() => {
                    console.log('[WorkoutDetailPage] 点击展开按钮:', {
                      exerciseId: exercise.id,
                      exerciseName: exercise.name,
                      currentIsExpanded: isExpanded
                    });
                    toggleExerciseExpansion(exercise.id);
                  }}
                />
              );
            })}
          </div>
        </section>

        {/* 肌肉详情 */}
        <section className="muscle-detail-section">
          <h2 className="section-title">目标肌群</h2>
          <MuscleInfoCard
            targetMuscleIds={workoutDetail.muscleData.targetMuscleIds}
            synergistMuscleIds={workoutDetail.muscleData.synergistMuscleIds}
            theme="light"
            className="workout-muscle-info"
          />
        </section>
      </main>
    </div>
  );
};

export default WorkoutDetailPage;