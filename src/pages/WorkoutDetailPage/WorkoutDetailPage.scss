// WorkoutDetailPage 样式
// iOS优先设计，遵循Apple HIG标准

.workout-detail-page {
  // iOS Safe Area 适配
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: var(--safe-area-inset-bottom, 0);
  padding-left: var(--safe-area-inset-left, 0);
  padding-right: var(--safe-area-inset-right, 0);
  background-color: var(--bg-primary);

  // iOS滚动优化
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;

  // 页面进入动画
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) var(--space-4);
    padding-top: calc(var(--safe-area-inset-top, 0px) + var(--space-2));
    background: var(--bg-surface);
    border-bottom: 1px solid var(--border-color);
    position: sticky; // 恢复sticky定位
    top: 0;
    z-index: 10;
    flex-shrink: 0; // 防止被压缩
    
    .back-button {
      min-width: 44px;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 22px;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      background: none;
      border: none;
      color: var(--accent-500);
      -webkit-tap-highlight-color: transparent;
      
      svg {
        width: 24px;
        height: 24px;
        stroke-width: 2.5;
      }
      
      &:hover {
        background: var(--bg-hover);
        transform: scale(1.05);
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
    
    .page-title {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0;
      text-align: center;
      flex: 1;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      
      .share-button,
      .more-button {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 22px;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        background: none;
        border: none;
        color: var(--text-secondary);
        -webkit-tap-highlight-color: transparent;
        
        svg {
          width: 20px;
          height: 20px;
          stroke-width: 2;
        }
        
        &:hover {
          background: var(--bg-hover);
          color: var(--text-primary);
          transform: scale(1.05);
        }
        
        &:active {
          transform: scale(0.95);
        }
      }
      
      .share-button {
        &:hover {
          color: var(--success-500);
        }
      }
      
      .header-dropdown {
        .dropdown-trigger .more-button {
          &:hover {
            color: var(--accent-500);
          }
        }
      }
    }
  }
  
  .page-content {
    padding: var(--space-4);
    padding-top: var(--space-2); // 减少顶部间距，因为header已经有safe area适配
    flex: 1;
    overflow-y: auto;

    // iOS Safe Area 额外适配 - 确保内容不被遮挡
    @supports (padding: max(0px)) {
      padding-top: max(var(--space-2), env(safe-area-inset-top, 0px));
    }
    
    // 去除所有iOS选中效果
    * {
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      user-select: none !important;
    }
    
    .section-title {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: var(--space-6) 0 var(--space-3);
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    .workout-stats-section,
    .exercises-section,
    .muscle-detail-section {
      margin-bottom: var(--space-6);
    }
  }
}

// iOS 专用样式：44px 最小触摸目标，符合 Apple HIG 标准

// 页面转场动画
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 基本组件样式
.social-section {
  background: var(--bg-surface);
  border-radius: 12px;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  border: 1px solid var(--border-color);

  .social-user {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: var(--space-2);

      .user-name {
        font-weight: var(--font-semibold);
        color: var(--text-primary);
      }

      .verified-badge {
        color: var(--accent-500);
        font-size: var(--text-sm);
      }
    }

    .follow-button {
      min-width: 44px;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
      background: var(--accent-500);
      color: white;
      border: none;
      border-radius: 20px;
      padding: 0 var(--space-4);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      
      &:active {
        transform: scale(0.98);
      }
    }
  }

  .post-content {
    margin-bottom: var(--space-3);

    p {
      color: var(--text-primary);
      line-height: 1.5;
      margin: 0;
    }
  }

  .post-stats {
    display: flex;
    gap: var(--space-4);

    .like-button {
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: var(--space-1);

      &.liked {
        color: var(--error-500);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.workout-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: var(--space-4);
  background: var(--bg-surface);
  border-radius: 12px;
  padding: var(--space-4);
  border: 1px solid var(--border-color);

  .stat-item {
    text-align: center;

    .stat-label {
      display: block;
      font-size: var(--text-sm);
      color: var(--text-secondary);
      margin-bottom: var(--space-1);
    }

    .stat-value {
      display: block;
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
    }
  }
}

.exercise-card {
  background: var(--bg-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin-bottom: var(--space-4);
  overflow: hidden;
  transition: all 0.3s ease;

  &.expanded {
    border-color: var(--accent-200);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .exercise-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    cursor: pointer;
    min-height: 44px;
    transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-tap-highlight-color: transparent;

    &:active {
      background: var(--bg-hover, rgba(0, 0, 0, 0.05));
      transform: scale(0.98);
    }

    .exercise-main-info {
      flex: 1;

      .exercise-name {
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-1);
      }

      .exercise-summary {
        display: flex;
        gap: var(--space-3);

        .sets-reps {
          font-size: var(--text-sm);
          color: var(--text-secondary);
        }

        .volume {
          font-size: var(--text-sm);
          color: var(--accent-500);
          font-weight: var(--font-medium);
        }
      }
    }

    .expand-button {
      color: var(--text-secondary);
      font-size: var(--text-sm);
      background: none;
      border: none;
      cursor: pointer;
    }
  }

  .exercise-details {
    border-top: 1px solid var(--border-color);
    padding: var(--space-4);

    .set-records-header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: var(--space-2);
      padding-bottom: var(--space-2);
      border-bottom: 1px solid var(--border-color);
      margin-bottom: var(--space-3);

      span {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-secondary);
        text-align: center;
      }
    }

    .set-record {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: var(--space-2);
      padding: var(--space-2) 0;
      border-bottom: 1px solid var(--border-light, rgba(0, 0, 0, 0.1));

      &:last-child {
        border-bottom: none;
      }

      span {
        font-size: var(--text-sm);
        text-align: center;
        color: var(--text-primary);
      }
    }

    .exercise-notes {
      margin-top: var(--space-4);
      padding-top: var(--space-4);
      border-top: 1px solid var(--border-color);

      h4 {
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2);
      }

      p {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

.muscle-info {
  background: var(--bg-surface);
  border-radius: 12px;
  padding: var(--space-4);
  border: 1px solid var(--border-color);

  p {
    color: var(--text-primary);
    margin: 0 0 var(--space-2);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 错误状态样式
.error-state, .not-found-state {
  text-align: center;
  padding: var(--space-8);

  .error-message, p {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
  }

  .retry-button {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--accent-500);
    color: white;
    border: none;
    border-radius: 22px;
    padding: 0 var(--space-6);
    font-weight: var(--font-medium);
    
    &:active {
      transform: scale(0.98);
    }
  }
}

// 加载状态样式
.loading-skeleton {
  .skeleton-block {
    height: 100px;
    background: var(--bg-secondary, rgba(0, 0, 0, 0.1));
    border-radius: 8px;
    margin-bottom: var(--space-4);
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 训练统计区域样式 - 与Feed页面保持一致
.workout-stats-section {
  margin-bottom: var(--space-4); // 与Feed页面一致

  // 使用与Feed页面相同的样式类名
  .feed-workout-stats {
    margin-bottom: var(--space-2); // 与Feed页面一致
    background: transparent; // 与Feed页面一致，移除背景
    border: none; // 移除边框
    padding: 0; // 移除内边距
    box-shadow: none; // 移除阴影
  }

  // 保留原有的workout-detail-stats样式作为备用
  .workout-detail-stats {
    background: var(--bg-surface);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: var(--space-4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: var(--border-hover);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// 肌肉详情区域样式
.muscle-detail-section {
  margin-bottom: var(--space-6);
  
  .section-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
  }
  
  .workout-muscle-info {
    background: var(--bg-surface);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: var(--space-4);
  }
}

// iOS专用媒体查询优化
@media only screen and (max-width: 768px) {
  .workout-stats-section {
    .workout-detail-stats {
      padding: var(--space-3);
      border-radius: 8px;
    }
  }
  
  .muscle-detail-section {
    .workout-muscle-info {
      padding: var(--space-3);
      border-radius: 8px;
    }
  }
}