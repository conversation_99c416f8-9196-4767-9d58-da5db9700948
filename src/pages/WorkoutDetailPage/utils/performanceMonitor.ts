/**
 * 性能监控工具
 * 
 * @fileoverview 用于监控和调试 WorkoutDetailPage 的性能问题
 * <AUTHOR> Team
 * @since 1.0.0
 */

interface PerformanceMetrics {
  executionCount: number;
  lastExecutionTime: number;
  totalExecutionTime: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
}

interface LoopDetectionResult {
  isLooping: boolean;
  executionCount: number;
  timeWindow: number;
  threshold: number;
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private executionTimes: Map<string, number[]> = new Map();
  
  /**
   * 开始监控一个操作
   */
  startOperation(operationName: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      this.recordExecution(operationName, executionTime);
    };
  }
  
  /**
   * 记录操作执行
   */
  private recordExecution(operationName: string, executionTime: number): void {
    const currentMetrics = this.metrics.get(operationName) || {
      executionCount: 0,
      lastExecutionTime: 0,
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      maxExecutionTime: 0,
      minExecutionTime: Infinity
    };
    
    currentMetrics.executionCount += 1;
    currentMetrics.lastExecutionTime = executionTime;
    currentMetrics.totalExecutionTime += executionTime;
    currentMetrics.averageExecutionTime = currentMetrics.totalExecutionTime / currentMetrics.executionCount;
    currentMetrics.maxExecutionTime = Math.max(currentMetrics.maxExecutionTime, executionTime);
    currentMetrics.minExecutionTime = Math.min(currentMetrics.minExecutionTime, executionTime);
    
    this.metrics.set(operationName, currentMetrics);
    
    // 记录执行时间历史
    const times = this.executionTimes.get(operationName) || [];
    times.push(Date.now());
    
    // 只保留最近50次执行记录
    if (times.length > 50) {
      times.shift();
    }
    
    this.executionTimes.set(operationName, times);
    
    // 输出性能日志
    if (currentMetrics.executionCount % 5 === 0) {
      console.log(`[PerformanceMonitor] ${operationName}:`, {
        count: currentMetrics.executionCount,
        avgTime: Math.round(currentMetrics.averageExecutionTime * 100) / 100,
        lastTime: Math.round(executionTime * 100) / 100,
        maxTime: Math.round(currentMetrics.maxExecutionTime * 100) / 100
      });
    }
  }
  
  /**
   * 检测是否存在循环执行
   */
  detectLoop(operationName: string, timeWindowMs: number = 5000, threshold: number = 10): LoopDetectionResult {
    const times = this.executionTimes.get(operationName) || [];
    const now = Date.now();
    const recentTimes = times.filter(time => now - time <= timeWindowMs);
    
    const isLooping = recentTimes.length >= threshold;
    
    if (isLooping) {
      console.warn(`[PerformanceMonitor] 检测到循环执行: ${operationName}`, {
        executionCount: recentTimes.length,
        timeWindow: timeWindowMs,
        threshold,
        recentTimes: recentTimes.slice(-5) // 显示最近5次执行时间
      });
    }
    
    return {
      isLooping,
      executionCount: recentTimes.length,
      timeWindow: timeWindowMs,
      threshold
    };
  }
  
  /**
   * 获取操作的性能指标
   */
  getMetrics(operationName: string): PerformanceMetrics | null {
    return this.metrics.get(operationName) || null;
  }
  
  /**
   * 获取所有性能指标
   */
  getAllMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics);
  }
  
  /**
   * 重置指定操作的指标
   */
  resetMetrics(operationName: string): void {
    this.metrics.delete(operationName);
    this.executionTimes.delete(operationName);
  }
  
  /**
   * 重置所有指标
   */
  resetAllMetrics(): void {
    this.metrics.clear();
    this.executionTimes.clear();
  }
  
  /**
   * 生成性能报告
   */
  generateReport(): string {
    const report: string[] = ['=== WorkoutDetailPage 性能报告 ==='];
    
    this.metrics.forEach((metrics, operationName) => {
      report.push(`\n${operationName}:`);
      report.push(`  执行次数: ${metrics.executionCount}`);
      report.push(`  平均耗时: ${Math.round(metrics.averageExecutionTime * 100) / 100}ms`);
      report.push(`  最大耗时: ${Math.round(metrics.maxExecutionTime * 100) / 100}ms`);
      report.push(`  最小耗时: ${Math.round(metrics.minExecutionTime * 100) / 100}ms`);
      report.push(`  总耗时: ${Math.round(metrics.totalExecutionTime * 100) / 100}ms`);
      
      // 检测循环
      const loopDetection = this.detectLoop(operationName);
      if (loopDetection.isLooping) {
        report.push(`  ⚠️  检测到循环执行！`);
      }
    });
    
    return report.join('\n');
  }
}

// 全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能监控装饰器Hook
 */
export function usePerformanceMonitor(operationName: string) {
  return {
    startOperation: () => performanceMonitor.startOperation(operationName),
    detectLoop: (timeWindow?: number, threshold?: number) => 
      performanceMonitor.detectLoop(operationName, timeWindow, threshold),
    getMetrics: () => performanceMonitor.getMetrics(operationName),
    generateReport: () => performanceMonitor.generateReport()
  };
}

/**
 * 防抖执行器
 */
export class DebounceExecutor {
  private timers: Map<string, NodeJS.Timeout> = new Map();
  
  /**
   * 防抖执行函数
   */
  execute<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number = 300,
    ...args: Parameters<T>
  ): void {
    // 清除之前的定时器
    const existingTimer = this.timers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }
    
    // 设置新的定时器
    const timer = setTimeout(() => {
      fn(...args);
      this.timers.delete(key);
    }, delay);
    
    this.timers.set(key, timer);
  }
  
  /**
   * 取消防抖执行
   */
  cancel(key: string): void {
    const timer = this.timers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }
  }
  
  /**
   * 清除所有防抖执行
   */
  cancelAll(): void {
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
  }
}

// 全局防抖执行器实例
export const debounceExecutor = new DebounceExecutor();
