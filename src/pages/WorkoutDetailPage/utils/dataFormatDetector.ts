/**
 * 数据格式检测工具
 * 
 * @fileoverview 用于检测和识别不同格式的Feed和Workout数据
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * 数据格式类型
 */
export type DataFormat = 'api_feed_post' | 'ui_feed_post' | 'api_workout_response' | 'unknown';

/**
 * 数据格式检测结果
 */
export interface DataFormatDetectionResult {
  format: DataFormat;
  hasWorkoutData: boolean;
  workoutDataPath?: string;
  confidence: number; // 0-1 之间的置信度
  details: {
    hasUser: boolean;
    hasContent: boolean;
    hasStats: boolean;
    hasRelatedWorkoutDetail: boolean;
    hasWorkoutExercises: boolean;
    hasExercises: boolean;
  };
}

/**
 * 检测数据格式
 * 
 * @param data 待检测的数据对象
 * @returns 检测结果
 */
export function detectDataFormat(data: any): DataFormatDetectionResult {
  if (!data || typeof data !== 'object') {
    return {
      format: 'unknown',
      hasWorkoutData: false,
      confidence: 0,
      details: {
        hasUser: false,
        hasContent: false,
        hasStats: false,
        hasRelatedWorkoutDetail: false,
        hasWorkoutExercises: false,
        hasExercises: false
      }
    };
  }

  const details = {
    hasUser: !!(data.user && typeof data.user === 'object'),
    hasContent: !!(data.content && typeof data.content === 'object'),
    hasStats: !!(data.stats && typeof data.stats === 'object'),
    hasRelatedWorkoutDetail: !!(data.related_workout_detail && typeof data.related_workout_detail === 'object'),
    hasWorkoutExercises: !!(data.related_workout_detail?.workout_exercises && Array.isArray(data.related_workout_detail.workout_exercises)),
    hasExercises: !!(data.exercises && Array.isArray(data.exercises))
  };

  // 检测UI FeedPost格式
  if (details.hasUser && details.hasContent && details.hasStats) {
    const hasWorkoutInContent = !!(data.content.workout && typeof data.content.workout === 'object');
    const hasExercisesInWorkout = !!(data.content.workout?.exercises && Array.isArray(data.content.workout.exercises));
    
    return {
      format: 'ui_feed_post',
      hasWorkoutData: hasWorkoutInContent,
      workoutDataPath: hasWorkoutInContent ? 'content.workout' : undefined,
      confidence: 0.9,
      details: {
        ...details,
        hasExercises: hasExercisesInWorkout
      }
    };
  }

  // 检测API FeedPost格式
  if (details.hasRelatedWorkoutDetail || data.related_workout_id) {
    return {
      format: 'api_feed_post',
      hasWorkoutData: details.hasRelatedWorkoutDetail,
      workoutDataPath: details.hasRelatedWorkoutDetail ? 'related_workout_detail' : undefined,
      confidence: details.hasWorkoutExercises ? 0.9 : 0.7,
      details
    };
  }

  // 检测API WorkoutResponse格式
  if (details.hasExercises && !details.hasUser && !details.hasContent) {
    return {
      format: 'api_workout_response',
      hasWorkoutData: true,
      workoutDataPath: 'exercises',
      confidence: 0.8,
      details
    };
  }

  return {
    format: 'unknown',
    hasWorkoutData: false,
    confidence: 0,
    details
  };
}

/**
 * 检查数据是否包含完整的训练详情
 * 
 * @param data 待检测的数据
 * @returns 是否包含完整训练详情
 */
export function hasCompleteWorkoutData(data: any): boolean {
  const detection = detectDataFormat(data);
  
  if (!detection.hasWorkoutData) {
    return false;
  }

  switch (detection.format) {
    case 'ui_feed_post':
      return !!(data.content?.workout?.exercises && 
                Array.isArray(data.content.workout.exercises) && 
                data.content.workout.exercises.length > 0);
    
    case 'api_feed_post':
      return !!(data.related_workout_detail?.workout_exercises && 
                Array.isArray(data.related_workout_detail.workout_exercises) && 
                data.related_workout_detail.workout_exercises.length > 0);
    
    case 'api_workout_response':
      return !!(data.exercises && 
                Array.isArray(data.exercises) && 
                data.exercises.length > 0);
    
    default:
      return false;
  }
}

/**
 * 从数据中提取训练ID
 * 
 * @param data 数据对象
 * @returns 训练ID或null
 */
export function extractWorkoutId(data: any): string | null {
  const detection = detectDataFormat(data);
  
  switch (detection.format) {
    case 'ui_feed_post':
      return data.content?.workout?.id?.toString() || null;
    
    case 'api_feed_post':
      return data.related_workout_id?.toString() || 
             data.related_workout_detail?.id?.toString() || 
             null;
    
    case 'api_workout_response':
      return data.id?.toString() || null;
    
    default:
      return null;
  }
}

/**
 * 获取数据格式的调试信息
 * 
 * @param data 数据对象
 * @returns 调试信息字符串
 */
export function getDataFormatDebugInfo(data: any): string {
  const detection = detectDataFormat(data);
  
  return `Format: ${detection.format} (${Math.round(detection.confidence * 100)}% confidence)
Has Workout Data: ${detection.hasWorkoutData}
Workout Data Path: ${detection.workoutDataPath || 'N/A'}
Details: ${JSON.stringify(detection.details, null, 2)}`;
}
