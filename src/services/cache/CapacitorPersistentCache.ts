/**
 * FitMaster Capacitor跨平台持久化缓存实现
 * 支持iOS Preferences和Web localStorage的统一存储接口
 */

import { Capacitor } from '@capacitor/core'
import { Preferences } from '@capacitor/preferences'
import {
  IPersistentCache,
  CacheItem,
  CacheStorageError,
  CacheSerializationError
} from './interfaces'
import PreferencesOptimizer from '../../utils/preferencesOptimizer'

/**
 * 存储统计信息
 */
interface StorageStats {
  totalKeys: number
  totalSize: number
  lastCleanup: number
  compressionRatio: number
}

/**
 * Capacitor跨平台持久化缓存实现
 */
export class CapacitorPersistentCache implements IPersistentCache {
  private static readonly CACHE_KEY_PREFIX = 'fitmaster_cache_'
  private static readonly METADATA_KEY = 'fitmaster_cache_metadata'
  private static readonly MAX_STORAGE_SIZE = 5 * 1024 * 1024 // 5MB iOS限制
  private static readonly COMPRESSION_THRESHOLD = 1024 // 1KB以上启用压缩

  private isNative: boolean
  private stats: StorageStats
  private keyCache = new Set<string>()

  constructor() {
    this.isNative = Capacitor.isNativePlatform()
    this.stats = {
      totalKeys: 0,
      totalSize: 0,
      lastCleanup: Date.now(),
      compressionRatio: 1.0
    }

    this.initialize()
    
    console.log('[CapacitorPersistentCache] 持久化缓存初始化完成', {
      platform: this.isNative ? 'native' : 'web',
      capacitor: Capacitor.getPlatform()
    })
  }

  async get<T>(key: string): Promise<CacheItem<T> | null> {
    try {
      const storageKey = this.buildStorageKey(key)
      let value: string | null

      if (this.isNative) {
        // 使用优化的Preferences操作
        value = await PreferencesOptimizer.get(storageKey)
      } else {
        value = localStorage.getItem(storageKey)
      }

      if (!value) {
        return null
      }

      // 反序列化和解压缩
      const item = await this.deserializeItem<T>(value)
      
      // 验证数据完整性
      if (!this.validateCacheItem(item)) {
        console.warn(`[CapacitorPersistentCache] 缓存项验证失败: ${key}`)
        await this.delete(key) // 清理损坏的数据
        return null
      }

      return item

    } catch (error) {
      console.error(`[CapacitorPersistentCache] 获取缓存失败: ${key}`, error)
      
      if (this.isSerializationError(error)) {
        // 序列化错误，清理损坏的数据
        await this.delete(key)
        return null
      }
      
      throw new CacheStorageError(`Failed to get cache item: ${key}`, key, error as Error)
    }
  }

  async set<T>(key: string, item: CacheItem<T>): Promise<void> {
    try {
      const storageKey = this.buildStorageKey(key)
      
      // 序列化和压缩
      const serializedValue = await this.serializeItem(item)
      
      // 检查存储大小限制
      await this.ensureStorageCapacity(serializedValue.length)

      if (this.isNative) {
        // 使用优化的Preferences操作
        await PreferencesOptimizer.set(storageKey, serializedValue)
      } else {
        localStorage.setItem(storageKey, serializedValue)
      }

      // 更新索引和统计
      this.keyCache.add(key)
      await this.updateStats(key, serializedValue.length)

      console.log(`[CapacitorPersistentCache] 缓存设置成功: ${key}`, {
        platform: this.isNative ? 'native' : 'web',
        size: serializedValue.length,
        compressed: serializedValue.length !== JSON.stringify(item).length
      })

    } catch (error) {
      console.error(`[CapacitorPersistentCache] 设置缓存失败: ${key}`, error)
      throw new CacheStorageError(`Failed to set cache item: ${key}`, key, error as Error)
    }
  }

  async delete(key: string): Promise<void> {
    try {
      const storageKey = this.buildStorageKey(key)

      if (this.isNative) {
        // 使用优化的Preferences操作
        await PreferencesOptimizer.remove(storageKey)
      } else {
        localStorage.removeItem(storageKey)
      }

      // 更新索引
      this.keyCache.delete(key)
      await this.updateStats()

      console.log(`[CapacitorPersistentCache] 缓存删除成功: ${key}`)

    } catch (error) {
      console.error(`[CapacitorPersistentCache] 删除缓存失败: ${key}`, error)
      throw new CacheStorageError(`Failed to delete cache item: ${key}`, key, error as Error)
    }
  }

  async clear(pattern?: string): Promise<void> {
    try {
      const keys = await this.keys(pattern)
      
      // 批量删除
      const deletePromises = keys.map(key => this.delete(key))
      await Promise.all(deletePromises)

      console.log(`[CapacitorPersistentCache] 缓存清理完成`, {
        pattern,
        deletedCount: keys.length
      })

    } catch (error) {
      console.error(`[CapacitorPersistentCache] 清理缓存失败`, error)
      throw new CacheStorageError('Failed to clear cache', undefined, error as Error)
    }
  }

  async keys(pattern?: string): Promise<string[]> {
    try {
      let allKeys: string[]

      if (this.isNative) {
        // Capacitor Preferences获取所有键
        const result = await Preferences.keys()
        allKeys = result.keys
          .filter(key => key.startsWith(CapacitorPersistentCache.CACHE_KEY_PREFIX))
          .map(key => key.replace(CapacitorPersistentCache.CACHE_KEY_PREFIX, ''))
      } else {
        // localStorage获取所有键
        allKeys = Object.keys(localStorage)
          .filter(key => key.startsWith(CapacitorPersistentCache.CACHE_KEY_PREFIX))
          .map(key => key.replace(CapacitorPersistentCache.CACHE_KEY_PREFIX, ''))
      }

      // 应用模式过滤
      if (pattern) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'))
        allKeys = allKeys.filter(key => regex.test(key))
      }

      return allKeys

    } catch (error) {
      console.error('[CapacitorPersistentCache] 获取键列表失败', error)
      throw new CacheStorageError('Failed to get cache keys', undefined, error as Error)
    }
  }

  async size(): Promise<number> {
    return this.keyCache.size
  }

  async cleanup(): Promise<void> {
    try {
      const now = Date.now()
      const allKeys = await this.keys()
      let cleanedCount = 0

      for (const key of allKeys) {
        try {
          const item = await this.get(key)
          
          // 检查是否过期
          if (!item || now > item.timestamp + item.ttl) {
            await this.delete(key)
            cleanedCount++
          }
        } catch (error) {
          // 如果读取失败，说明数据可能损坏，直接删除
          console.warn(`[CapacitorPersistentCache] 清理损坏数据: ${key}`)
          await this.delete(key)
          cleanedCount++
        }
      }

      this.stats.lastCleanup = now
      await this.saveMetadata()

      console.log('[CapacitorPersistentCache] 清理完成', {
        cleanedCount,
        remainingCount: allKeys.length - cleanedCount
      })

    } catch (error) {
      console.error('[CapacitorPersistentCache] 清理过程出错', error)
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageStats> {
    await this.updateStats()
    return { ...this.stats }
  }

  /**
   * 压缩存储空间（删除过期和低优先级数据）
   */
  async compress(): Promise<void> {
    try {
      const allKeys = await this.keys()
      const items = await Promise.all(
        allKeys.map(async key => ({
          key,
          item: await this.get(key)
        }))
      )

      // 按优先级和访问时间排序
      const sortedItems = items
        .filter(({ item }) => item !== null)
        .sort((a, b) => {
          const itemA = a.item!
          const itemB = b.item!
          
          // 优先级权重
          const priorityWeight = { low: 1, medium: 2, high: 3 }
          const priorityA = priorityWeight[itemA.priority] || 2
          const priorityB = priorityWeight[itemB.priority] || 2
          
          // 综合评分（优先级 + 访问频率 + 新鲜度）
          const scoreA = priorityA * (itemA.accessCount || 1) * (itemA.lastAccessed || itemA.timestamp)
          const scoreB = priorityB * (itemB.accessCount || 1) * (itemB.lastAccessed || itemB.timestamp)
          
          return scoreB - scoreA // 降序排列
        })

      // 保留前70%的数据
      const keepCount = Math.floor(sortedItems.length * 0.7)
      const toDelete = sortedItems.slice(keepCount)

      for (const { key } of toDelete) {
        await this.delete(key)
      }

      console.log('[CapacitorPersistentCache] 存储压缩完成', {
        originalCount: items.length,
        deletedCount: toDelete.length,
        remainingCount: keepCount
      })

    } catch (error) {
      console.error('[CapacitorPersistentCache] 存储压缩失败', error)
    }
  }

  // ===== 私有方法 =====

  private async initialize(): Promise<void> {
    try {
      // 加载元数据
      await this.loadMetadata()
      
      // 更新键缓存
      const keys = await this.keys()
      this.keyCache = new Set(keys)
      
      // 更新统计信息
      await this.updateStats()

    } catch (error) {
      console.warn('[CapacitorPersistentCache] 初始化时出现警告', error)
    }
  }

  private buildStorageKey(key: string): string {
    return `${CapacitorPersistentCache.CACHE_KEY_PREFIX}${key}`
  }

  private async serializeItem<T>(item: CacheItem<T>): Promise<string> {
    try {
      // 使用增强的序列化，支持 Date 对象
      const { cacheSerializationEnhancer } = await import('../../utils/cacheSerializationEnhancer')
      const jsonString = cacheSerializationEnhancer.stringify(item)

      // 如果数据较大，启用压缩
      if (jsonString.length > CapacitorPersistentCache.COMPRESSION_THRESHOLD) {
        return await this.compressData(jsonString)
      }

      return jsonString

    } catch (error) {
      console.error('[CapacitorPersistentCache] 序列化失败，尝试降级到标准JSON:', error)
      try {
        // 降级到标准 JSON 序列化
        const jsonString = JSON.stringify(item)
        if (jsonString.length > CapacitorPersistentCache.COMPRESSION_THRESHOLD) {
          return await this.compressData(jsonString)
        }
        return jsonString
      } catch (fallbackError) {
        throw new CacheSerializationError('Failed to serialize cache item', item.key, fallbackError as Error)
      }
    }
  }

  private async deserializeItem<T>(value: string): Promise<CacheItem<T>> {
    try {
      // 尝试解压缩
      const jsonString = await this.decompressData(value)

      // 使用增强的反序列化，支持 Date 对象恢复
      const { cacheSerializationEnhancer } = await import('../../utils/cacheSerializationEnhancer')
      const item = cacheSerializationEnhancer.parse<CacheItem<T>>(jsonString)

      // 验证反序列化结果
      const validation = cacheSerializationEnhancer.validate(item)
      if (!validation.isValid && validation.errors.length > 0) {
        console.warn('[CapacitorPersistentCache] 缓存数据验证警告:', validation.errors)
      }

      return item

    } catch (error) {
      console.error('[CapacitorPersistentCache] 增强反序列化失败，尝试标准JSON:', error)
      try {
        // 降级到标准 JSON 反序列化
        const jsonString = await this.decompressData(value)
        const item = JSON.parse(jsonString) as CacheItem<T>

        // 尝试修复数据类型问题
        const { sanitizeDateFields } = await import('../../utils/cacheSerializationEnhancer')
        return sanitizeDateFields(item) as CacheItem<T>

      } catch (fallbackError) {
        throw new CacheSerializationError('Failed to deserialize cache item', undefined, fallbackError as Error)
      }
    }
  }

  private async compressData(data: string): Promise<string> {
    // 简单的压缩实现（生产环境可以使用更高效的压缩算法）
    try {
      // 这里可以集成LZ-string或其他压缩库
      // 暂时返回原始数据，标记为已压缩
      return `__COMPRESSED__${data}`
    } catch (error) {
      console.warn('[CapacitorPersistentCache] 压缩失败，使用原始数据', error)
      return data
    }
  }

  private async decompressData(data: string): Promise<string> {
    try {
      if (data.startsWith('__COMPRESSED__')) {
        return data.replace('__COMPRESSED__', '')
      }
      return data
    } catch (error) {
      console.warn('[CapacitorPersistentCache] 解压缩失败，尝试使用原始数据', error)
      return data
    }
  }

  private validateCacheItem<T>(item: any): item is CacheItem<T> {
    return (
      item &&
      typeof item === 'object' &&
      typeof item.key === 'string' &&
      typeof item.timestamp === 'number' &&
      typeof item.ttl === 'number' &&
      Array.isArray(item.tags) &&
      item.data !== undefined
    )
  }

  private isSerializationError(error: any): boolean {
    return error instanceof SyntaxError || 
           (error instanceof Error && error.message.includes('JSON'))
  }

  private async ensureStorageCapacity(newDataSize: number): Promise<void> {
    const currentSize = await this.calculateStorageSize()
    
    if (currentSize + newDataSize > CapacitorPersistentCache.MAX_STORAGE_SIZE) {
      console.warn('[CapacitorPersistentCache] 存储空间不足，执行压缩')
      await this.compress()
    }
  }

  private async calculateStorageSize(): Promise<number> {
    // 简化的大小计算，实际应该更精确
    return this.keyCache.size * 1024 // 假设每项平均1KB
  }

  private async updateStats(newKey?: string, newSize?: number): Promise<void> {
    this.stats.totalKeys = this.keyCache.size
    
    if (newKey && newSize) {
      this.stats.totalSize += newSize
    } else {
      // 重新计算总大小（成本较高，仅在必要时执行）
      this.stats.totalSize = await this.calculateStorageSize()
    }
  }

  private async loadMetadata(): Promise<void> {
    try {
      const metadataKey = CapacitorPersistentCache.METADATA_KEY
      let metadata: string | null

      if (this.isNative) {
        const result = await Preferences.get({ key: metadataKey })
        metadata = result.value
      } else {
        metadata = localStorage.getItem(metadataKey)
      }

      if (metadata) {
        const parsed = JSON.parse(metadata)
        this.stats = { ...this.stats, ...parsed }
      }

    } catch (error) {
      console.warn('[CapacitorPersistentCache] 加载元数据失败', error)
    }
  }

  private async saveMetadata(): Promise<void> {
    try {
      const metadataKey = CapacitorPersistentCache.METADATA_KEY
      const metadata = JSON.stringify(this.stats)

      if (this.isNative) {
        await Preferences.set({ key: metadataKey, value: metadata })
      } else {
        localStorage.setItem(metadataKey, metadata)
      }

    } catch (error) {
      console.warn('[CapacitorPersistentCache] 保存元数据失败', error)
    }
  }
}