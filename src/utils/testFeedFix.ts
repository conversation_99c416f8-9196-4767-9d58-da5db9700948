/**
 * Feed 修复测试工具
 * 用于测试时间戳修复逻辑
 */

import { FeedPost } from '../models/ui/feed/Post';

/**
 * 模拟有问题的时间戳数据
 */
export const mockProblematicPosts = [
  {
    id: '1',
    timestamp: { __DATE__: '2024-01-01T10:00:00.000Z' }, // 序列化增强器格式
    user: { id: '1', name: 'Test User', username: 'test', avatar: '/test.jpg' },
    content: { text: 'Test post 1' },
    stats: { likes: 5, comments: 2, shares: 1 },
    isLiked: false,
    visibility: 'everyone' as const,
    tags: []
  },
  {
    id: '2',
    timestamp: { value: '2024-01-02T10:00:00.000Z' }, // 对象属性格式
    user: { id: '2', name: 'Test User 2', username: 'test2', avatar: '/test2.jpg' },
    content: { text: 'Test post 2' },
    stats: { likes: 3, comments: 1, shares: 0 },
    isLiked: true,
    visibility: 'everyone' as const,
    tags: []
  },
  {
    id: '3',
    timestamp: '2024-01-03T10:00:00.000Z', // 字符串格式
    user: { id: '3', name: 'Test User 3', username: 'test3', avatar: '/test3.jpg' },
    content: { text: 'Test post 3' },
    stats: { likes: 8, comments: 4, shares: 2 },
    isLiked: false,
    visibility: 'everyone' as const,
    tags: []
  },
  {
    id: '4',
    timestamp: new Date('2024-01-04T10:00:00.000Z'), // 正确的 Date 对象
    user: { id: '4', name: 'Test User 4', username: 'test4', avatar: '/test4.jpg' },
    content: { text: 'Test post 4' },
    stats: { likes: 12, comments: 6, shares: 3 },
    isLiked: true,
    visibility: 'everyone' as const,
    tags: []
  }
];

/**
 * 测试时间戳修复逻辑
 */
export function testTimestampFix(): void {
  console.log('[testTimestampFix] 开始测试时间戳修复逻辑...');
  
  mockProblematicPosts.forEach((post, index) => {
    console.log(`[testTimestampFix] 测试帖子 ${index + 1}:`);
    console.log('  原始时间戳:', post.timestamp);
    console.log('  时间戳类型:', typeof post.timestamp);
    console.log('  是否为 Date:', post.timestamp instanceof Date);
    
    // 应用修复逻辑
    let fixedTimestamp = post.timestamp;
    
    if (fixedTimestamp && typeof fixedTimestamp === 'object' && !(fixedTimestamp instanceof Date)) {
      // 检查是否是序列化增强器的格式
      if ((fixedTimestamp as any).__DATE__) {
        fixedTimestamp = new Date((fixedTimestamp as any).__DATE__);
        console.log('  修复方式: 从 __DATE__ 属性');
      } else {
        // 尝试其他可能的属性
        const dateProps = ['value', 'date', 'time', 'timestamp', 'iso'];
        let fixed = false;
        for (const prop of dateProps) {
          if ((fixedTimestamp as any)[prop]) {
            try {
              const date = new Date((fixedTimestamp as any)[prop]);
              if (!isNaN(date.getTime())) {
                fixedTimestamp = date;
                fixed = true;
                console.log(`  修复方式: 从 ${prop} 属性`);
                break;
              }
            } catch (e) {
              // 继续尝试下一个属性
            }
          }
        }
        
        if (!fixed) {
          // 使用当前时间作为后备
          fixedTimestamp = new Date();
          console.log('  修复方式: 使用当前时间（后备方案）');
        }
      }
    } else if (typeof fixedTimestamp === 'string') {
      try {
        fixedTimestamp = new Date(fixedTimestamp);
        console.log('  修复方式: 从字符串转换');
      } catch (e) {
        fixedTimestamp = new Date();
        console.log('  修复方式: 字符串转换失败，使用当前时间');
      }
    } else if (!fixedTimestamp) {
      fixedTimestamp = new Date();
      console.log('  修复方式: 时间戳为空，使用当前时间');
    } else {
      console.log('  修复方式: 无需修复，已经是有效的 Date 对象');
    }
    
    console.log('  修复后时间戳:', fixedTimestamp);
    console.log('  修复后类型:', typeof fixedTimestamp);
    console.log('  是否为有效 Date:', fixedTimestamp instanceof Date && !isNaN(fixedTimestamp.getTime()));
    
    // 测试 toISOString 调用
    try {
      const isoString = (fixedTimestamp as Date).toISOString();
      console.log('  ISO 字符串:', isoString);
      console.log('  ✅ toISOString() 调用成功');
    } catch (error) {
      console.error('  ❌ toISOString() 调用失败:', error);
    }
    
    console.log('  ---');
  });
  
  console.log('[testTimestampFix] 测试完成');
}

/**
 * 在控制台中暴露测试函数
 */
if (typeof window !== 'undefined') {
  (window as any).testTimestampFix = testTimestampFix;
  console.log('[testFeedFix] 测试函数已暴露到全局: testTimestampFix()');
}
