/**
 * 头像处理工具函数
 * 
 * @fileoverview 提供全局统一的头像处理逻辑，包括默认头像设置和URL验证
 * <AUTHOR> Team
 * @since 1.0.0
 */

// 默认头像路径
const DEFAULT_AVATAR = '/src/assets/icon/avatar.png';

/**
 * 获取有效的头像URL
 * 如果头像URL为空或无效，返回默认头像
 * 
 * @param avatarUrl - 原始头像URL
 * @returns 有效的头像URL
 */
export const getValidAvatarUrl = (avatarUrl: string | null | undefined): string => {
  // 如果头像URL为空或只包含空白字符
  if (!avatarUrl || avatarUrl.trim() === '') {
    return DEFAULT_AVATAR;
  }
  
  // 检查是否为无效的示例链接
  const invalidPatterns = [
    'example.com',
    'placeholder',
    'default-avatar',
    'mock_avatar',
    'test_avatar'
  ];
  
  const normalizedUrl = avatarUrl.toLowerCase();
  for (const pattern of invalidPatterns) {
    if (normalizedUrl.includes(pattern)) {
      return DEFAULT_AVATAR;
    }
  }
  
  // 检查是否为有效的图片URL格式
  try {
    const url = new URL(avatarUrl);
    // 检查是否有图片扩展名或是有效的图片服务域名
    const validImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    const hasValidExtension = validImageExtensions.some(ext => 
      url.pathname.toLowerCase().endsWith(ext)
    );
    
    const validImageDomains = [
      'cdn.',
      'img.',
      'avatar.',
      'profile.',
      'gravatar.com',
      'imgur.com',
      'cloudinary.com'
    ];
    
    const hasValidDomain = validImageDomains.some(domain => 
      url.hostname.includes(domain)
    );
    
    // 如果有有效扩展名或来自已知图片服务域名，认为是有效的
    if (hasValidExtension || hasValidDomain) {
      return avatarUrl;
    }
  } catch (error) {
    // URL解析失败，可能是相对路径或其他格式
    // 如果以 / 开头，认为是相对路径，可能有效
    if (avatarUrl.startsWith('/') || avatarUrl.startsWith('./')) {
      return avatarUrl;
    }
  }
  
  // 其他情况返回默认头像
  return DEFAULT_AVATAR;
};

/**
 * 为Avatar组件创建头像props
 * 包含错误处理和兜底逻辑
 * 
 * @param avatarUrl - 原始头像URL
 * @param userName - 用户名，用于生成备用显示
 * @returns Avatar组件所需的props
 */
export const createAvatarProps = (
  avatarUrl: string | null | undefined,
  userName: string = 'User'
) => {
  const validUrl = getValidAvatarUrl(avatarUrl);
  
  return {
    src: validUrl,
    alt: `${userName}的头像`,
    fallback: userName.charAt(0).toUpperCase(),
    // 头像加载失败时的处理
    onError: (event: React.SyntheticEvent<HTMLImageElement>) => {
      const img = event.target as HTMLImageElement;
      if (img.src !== DEFAULT_AVATAR) {
        console.warn(`头像加载失败: ${img.src}，使用默认头像`);
        img.src = DEFAULT_AVATAR;
      }
    }
  };
};

/**
 * 检查头像URL是否为默认头像
 * 
 * @param avatarUrl - 头像URL
 * @returns 是否为默认头像
 */
export const isDefaultAvatar = (avatarUrl: string | null | undefined): boolean => {
  return !avatarUrl || getValidAvatarUrl(avatarUrl) === DEFAULT_AVATAR;
};

/**
 * 获取默认头像路径
 * 
 * @returns 默认头像路径
 */
export const getDefaultAvatarPath = (): string => {
  return DEFAULT_AVATAR;
};