/**
 * Feed 数据处理器
 * 专门处理 Feed 数据的类型安全和错误恢复
 * 
 * @fileoverview Feed 数据处理工具
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { FeedPost } from '../models/ui/feed/Post';
import { 
  validateFeedPost, 
  repairFeedPost, 
  repairFeedPosts,
  ValidationResult,
  RepairResult 
} from './dataTypeValidator';
import { safeToISOString, createSafeDate } from './dateValidation';

/**
 * Feed 数据处理配置
 */
export interface FeedDataProcessorConfig {
  /** 是否启用自动修复 */
  autoRepair: boolean;
  /** 是否记录详细日志 */
  verbose: boolean;
  /** 是否启用严格模式（验证失败时抛出错误） */
  strictMode: boolean;
  /** 最大重试次数 */
  maxRetries: number;
}

/**
 * 默认配置
 */
export const DEFAULT_FEED_PROCESSOR_CONFIG: FeedDataProcessorConfig = {
  autoRepair: true,
  verbose: false,
  strictMode: false,
  maxRetries: 3
};

/**
 * Feed 数据处理结果
 */
export interface FeedProcessResult<T> {
  success: boolean;
  data: T | null;
  errors: string[];
  warnings: string[];
  repaired: string[];
  originalCount?: number;
  processedCount?: number;
}

/**
 * Feed 数据处理器类
 */
export class FeedDataProcessor {
  private config: FeedDataProcessorConfig;
  
  constructor(config: Partial<FeedDataProcessorConfig> = {}) {
    this.config = { ...DEFAULT_FEED_PROCESSOR_CONFIG, ...config };
  }
  
  /**
   * 处理单个 Feed 帖子
   */
  processFeedPost(post: any): FeedProcessResult<FeedPost> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const repaired: string[] = [];
    
    if (this.config.verbose) {
      console.log('[FeedDataProcessor] 开始处理帖子:', post?.id);
    }
    
    try {
      // 1. 验证数据
      const validation = validateFeedPost(post);
      warnings.push(...validation.warnings);
      
      if (!validation.isValid) {
        if (this.config.strictMode) {
          errors.push(...validation.errors);
          return { success: false, data: null, errors, warnings, repaired };
        }
        
        // 非严格模式下尝试修复
        if (this.config.autoRepair) {
          const repair = repairFeedPost(post);
          if (repair.success && repair.data) {
            repaired.push(...repair.repaired);
            if (this.config.verbose) {
              console.log('[FeedDataProcessor] 帖子修复成功:', repair.repaired);
            }
            return { success: true, data: repair.data, errors, warnings, repaired };
          } else {
            errors.push(...repair.errors);
          }
        } else {
          errors.push(...validation.errors);
        }
        
        return { success: false, data: null, errors, warnings, repaired };
      }
      
      // 2. 数据已经有效，进行额外的安全处理
      const processedPost = this.enhancePostSafety(post);
      
      return { success: true, data: processedPost, errors, warnings, repaired };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`处理帖子时发生异常: ${errorMessage}`);
      
      if (this.config.verbose) {
        console.error('[FeedDataProcessor] 处理帖子异常:', error);
      }
      
      return { success: false, data: null, errors, warnings, repaired };
    }
  }
  
  /**
   * 处理 Feed 帖子数组
   */
  processFeedPosts(posts: any[]): FeedProcessResult<FeedPost[]> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const repaired: string[] = [];
    const processedPosts: FeedPost[] = [];
    
    if (!Array.isArray(posts)) {
      errors.push('输入数据不是数组');
      return { 
        success: false, 
        data: null, 
        errors, 
        warnings, 
        repaired,
        originalCount: 0,
        processedCount: 0
      };
    }
    
    if (this.config.verbose) {
      console.log(`[FeedDataProcessor] 开始处理 ${posts.length} 个帖子`);
    }
    
    const originalCount = posts.length;
    let successCount = 0;
    
    for (let i = 0; i < posts.length; i++) {
      const post = posts[i];
      const result = this.processFeedPost(post);
      
      if (result.success && result.data) {
        processedPosts.push(result.data);
        successCount++;
        
        // 合并修复信息
        if (result.repaired.length > 0) {
          repaired.push(`帖子 ${i}: ${result.repaired.join(', ')}`);
        }
      } else {
        // 记录失败的帖子
        errors.push(`帖子 ${i}: ${result.errors.join(', ')}`);
        
        if (this.config.verbose) {
          console.warn(`[FeedDataProcessor] 帖子 ${i} 处理失败:`, result.errors);
        }
      }
      
      // 合并警告信息
      if (result.warnings.length > 0) {
        warnings.push(`帖子 ${i}: ${result.warnings.join(', ')}`);
      }
    }
    
    const success = processedPosts.length > 0;
    
    if (this.config.verbose) {
      console.log(`[FeedDataProcessor] 处理完成: ${successCount}/${originalCount} 成功`);
    }
    
    return {
      success,
      data: processedPosts,
      errors,
      warnings,
      repaired,
      originalCount,
      processedCount: processedPosts.length
    };
  }
  
  /**
   * 增强帖子的安全性
   * 添加额外的防护措施
   */
  private enhancePostSafety(post: FeedPost): FeedPost {
    const enhanced = { ...post };
    
    // 确保时间戳是 Date 对象
    if (!(enhanced.timestamp instanceof Date)) {
      const safeDate = createSafeDate(enhanced.timestamp as any);
      if (safeDate) {
        enhanced.timestamp = safeDate;
      } else {
        // 使用当前时间作为后备
        enhanced.timestamp = new Date();
        console.warn('[FeedDataProcessor] 时间戳无效，使用当前时间');
      }
    }
    
    // 确保用户头像有默认值
    if (!enhanced.user.avatar) {
      enhanced.user.avatar = '/api/placeholder/40/40';
    }
    
    // 确保统计数据为非负数
    if (enhanced.stats.likes < 0) enhanced.stats.likes = 0;
    if (enhanced.stats.comments < 0) enhanced.stats.comments = 0;
    if (enhanced.stats.shares < 0) enhanced.stats.shares = 0;
    if (enhanced.stats.views && enhanced.stats.views < 0) enhanced.stats.views = 0;
    if (enhanced.stats.reports && enhanced.stats.reports < 0) enhanced.stats.reports = 0;
    
    // 确保标签是数组
    if (!Array.isArray(enhanced.tags)) {
      enhanced.tags = [];
    }
    
    // 确保图片数组有效
    if (enhanced.images && !Array.isArray(enhanced.images)) {
      enhanced.images = [];
    }
    
    return enhanced;
  }
  
  /**
   * 安全获取时间戳字符串
   * 用于组件渲染 - 优化版本，优先使用原始数据的时间字段
   */
  static safeGetTimestamp(post: any): string {
    try {
      if (!post) {
        return new Date().toISOString();
      }
      
      // 优先使用 created_at 字段（来自API的标准字段）
      if (post.created_at) {
        if (typeof post.created_at === 'string') {
          // 验证是否为有效的日期字符串
          const date = createSafeDate(post.created_at);
          if (date) {
            return post.created_at; // 直接返回原始字符串，避免时区转换问题
          }
        }
      }
      
      // 其次使用 updated_at 字段
      if (post.updated_at) {
        if (typeof post.updated_at === 'string') {
          const date = createSafeDate(post.updated_at);
          if (date) {
            return post.updated_at;
          }
        }
      }
      
      // 再次尝试 timestamp 字段
      if (post.timestamp) {
        // 如果是 Date 对象
        if (post.timestamp instanceof Date) {
          return post.timestamp.toISOString();
        }
        
        // 如果是字符串，尝试转换
        if (typeof post.timestamp === 'string') {
          const date = createSafeDate(post.timestamp);
          if (date) {
            return post.timestamp; // 返回原始字符串
          }
        }
        
        // 使用 safeToISOString 工具函数处理其他格式
        const safeTimestamp = safeToISOString(post.timestamp);
        if (safeTimestamp) {
          return safeTimestamp;
        }
      }
      
      // 最后的后备方案
      console.warn('[FeedDataProcessor] 无法获取有效时间戳，使用当前时间', { 
        post_id: post.id, 
        available_fields: Object.keys(post) 
      });
      return new Date().toISOString();
      
    } catch (error) {
      console.error('[FeedDataProcessor] 获取时间戳时发生错误:', error);
      return new Date().toISOString();
    }
  }
  
  /**
   * 批量验证帖子数据
   * 不进行修复，仅返回验证结果
   */
  validateFeedPosts(posts: any[]): ValidationResult & { validCount: number; invalidCount: number } {
    const errors: string[] = [];
    const warnings: string[] = [];
    let validCount = 0;
    let invalidCount = 0;
    
    if (!Array.isArray(posts)) {
      errors.push('输入数据不是数组');
      return { isValid: false, errors, warnings, validCount: 0, invalidCount: 0 };
    }
    
    posts.forEach((post, index) => {
      const validation = validateFeedPost(post);
      if (validation.isValid) {
        validCount++;
      } else {
        invalidCount++;
        errors.push(`帖子 ${index}: ${validation.errors.join(', ')}`);
      }
      
      if (validation.warnings.length > 0) {
        warnings.push(`帖子 ${index}: ${validation.warnings.join(', ')}`);
      }
    });
    
    return {
      isValid: invalidCount === 0,
      errors,
      warnings,
      validCount,
      invalidCount
    };
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<FeedDataProcessorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
  
  /**
   * 获取当前配置
   */
  getConfig(): FeedDataProcessorConfig {
    return { ...this.config };
  }
}

/**
 * 默认的 Feed 数据处理器实例
 */
export const feedDataProcessor = new FeedDataProcessor();

/**
 * 便捷函数：处理单个帖子
 */
export function processFeedPost(post: any): FeedProcessResult<FeedPost> {
  return feedDataProcessor.processFeedPost(post);
}

/**
 * 便捷函数：处理帖子数组
 */
export function processFeedPosts(posts: any[]): FeedProcessResult<FeedPost[]> {
  return feedDataProcessor.processFeedPosts(posts);
}

/**
 * 便捷函数：安全获取时间戳
 */
export function safeGetTimestamp(post: any): string {
  return FeedDataProcessor.safeGetTimestamp(post);
}
