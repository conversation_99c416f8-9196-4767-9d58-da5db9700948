/**
 * 缓存数据调试工具
 * 用于检查和分析缓存中的数据结构
 */

import { Preferences } from '@capacitor/preferences';
import { Capacitor } from '@capacitor/core';

/**
 * 检查缓存中的 Feed 数据
 */
export async function debugFeedCacheData(): Promise<void> {
  console.log('[debugFeedCacheData] 开始检查缓存数据...');
  
  try {
    const isNative = Capacitor.isNativePlatform();
    console.log('[debugFeedCacheData] 平台:', isNative ? 'native' : 'web');
    
    // 查找所有相关的缓存键
    const cacheKeys = [
      'fitmaster_cache_feed:posts:filter=all&page=0:v1.0',
      'fitmaster_cache_community:posts:limit=20&skip=0:v1.0'
    ];
    
    for (const key of cacheKeys) {
      console.log(`[debugFeedCacheData] 检查缓存键: ${key}`);
      
      let rawData: string | null = null;
      
      if (isNative) {
        const result = await Preferences.get({ key });
        rawData = result.value;
      } else {
        rawData = localStorage.getItem(key);
      }
      
      if (rawData) {
        console.log(`[debugFeedCacheData] 找到缓存数据，长度: ${rawData.length}`);
        
        try {
          // 尝试解析数据
          const parsed = JSON.parse(rawData);
          console.log(`[debugFeedCacheData] 解析成功:`, {
            type: typeof parsed,
            keys: Object.keys(parsed),
            hasData: !!parsed.data,
            dataType: typeof parsed.data
          });
          
          // 如果有 data 字段，检查其结构
          if (parsed.data) {
            if (Array.isArray(parsed.data)) {
              console.log(`[debugFeedCacheData] data 是数组，长度: ${parsed.data.length}`);
              if (parsed.data.length > 0) {
                const firstItem = parsed.data[0];
                console.log(`[debugFeedCacheData] 第一个项目:`, {
                  type: typeof firstItem,
                  keys: Object.keys(firstItem),
                  hasTimestamp: 'timestamp' in firstItem,
                  timestamp: firstItem.timestamp,
                  timestampType: typeof firstItem.timestamp,
                  timestampConstructor: firstItem.timestamp?.constructor?.name
                });
              }
            } else if (parsed.data.posts && Array.isArray(parsed.data.posts)) {
              console.log(`[debugFeedCacheData] data.posts 是数组，长度: ${parsed.data.posts.length}`);
              if (parsed.data.posts.length > 0) {
                const firstPost = parsed.data.posts[0];
                console.log(`[debugFeedCacheData] 第一个帖子:`, {
                  type: typeof firstPost,
                  keys: Object.keys(firstPost),
                  hasTimestamp: 'timestamp' in firstPost,
                  timestamp: firstPost.timestamp,
                  timestampType: typeof firstPost.timestamp,
                  timestampConstructor: firstPost.timestamp?.constructor?.name,
                  timestampKeys: firstPost.timestamp && typeof firstPost.timestamp === 'object' 
                    ? Object.keys(firstPost.timestamp) 
                    : null
                });
              }
            }
          }
          
        } catch (parseError) {
          console.error(`[debugFeedCacheData] 解析失败:`, parseError);
        }
      } else {
        console.log(`[debugFeedCacheData] 缓存键 ${key} 没有数据`);
      }
    }
    
    // 检查所有缓存键
    if (!isNative) {
      console.log('[debugFeedCacheData] 检查所有 localStorage 键:');
      const allKeys = Object.keys(localStorage);
      const cacheRelatedKeys = allKeys.filter(key => 
        key.includes('fitmaster') || 
        key.includes('feed') || 
        key.includes('community')
      );
      console.log('[debugFeedCacheData] 相关缓存键:', cacheRelatedKeys);
    }
    
  } catch (error) {
    console.error('[debugFeedCacheData] 检查缓存数据时出错:', error);
  }
}

/**
 * 清理所有 Feed 相关缓存
 */
export async function clearFeedCache(): Promise<void> {
  console.log('[clearFeedCache] 开始清理 Feed 缓存...');
  
  try {
    const isNative = Capacitor.isNativePlatform();
    
    if (isNative) {
      // 在原生环境中，我们需要知道具体的键名
      const keysToRemove = [
        'fitmaster_cache_feed:posts:filter=all&page=0:v1.0',
        'fitmaster_cache_community:posts:limit=20&skip=0:v1.0'
      ];
      
      for (const key of keysToRemove) {
        await Preferences.remove({ key });
        console.log(`[clearFeedCache] 已删除缓存键: ${key}`);
      }
    } else {
      // 在 Web 环境中，我们可以遍历所有键
      const allKeys = Object.keys(localStorage);
      const feedKeys = allKeys.filter(key => 
        key.includes('fitmaster_cache') && 
        (key.includes('feed') || key.includes('community'))
      );
      
      for (const key of feedKeys) {
        localStorage.removeItem(key);
        console.log(`[clearFeedCache] 已删除缓存键: ${key}`);
      }
    }
    
    console.log('[clearFeedCache] Feed 缓存清理完成');
    
  } catch (error) {
    console.error('[clearFeedCache] 清理缓存时出错:', error);
  }
}

/**
 * 在控制台中暴露调试函数
 */
if (typeof window !== 'undefined') {
  (window as any).debugFeedCache = debugFeedCacheData;
  (window as any).clearFeedCache = clearFeedCache;
  console.log('[debugCacheData] 调试函数已暴露到全局: debugFeedCache(), clearFeedCache()');
}
