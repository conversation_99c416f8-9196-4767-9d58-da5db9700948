/**
 * 数据类型验证器
 * 专门用于验证和修复从缓存恢复的数据类型
 * 
 * @fileoverview 数据类型验证和修复工具
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { FeedPost, FeedPostStats, FeedPostContent } from '../models/ui/feed/Post';
import { FeedUser } from '../models/ui/feed/User';
import { createSafeDate, isValidDate } from './dateValidation';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 数据修复结果接口
 */
export interface RepairResult<T> {
  success: boolean;
  data: T | null;
  errors: string[];
  repaired: string[];
}

/**
 * 验证 Date 对象或日期字符串
 */
export function validateTimestamp(timestamp: any, fieldName: string = 'timestamp'): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (timestamp === null || timestamp === undefined) {
    errors.push(`${fieldName} 不能为空`);
    return { isValid: false, errors, warnings };
  }
  
  // 如果是 Date 对象
  if (timestamp instanceof Date) {
    if (!isValidDate(timestamp)) {
      errors.push(`${fieldName} 是无效的 Date 对象`);
    }
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  // 如果是字符串
  if (typeof timestamp === 'string') {
    const date = createSafeDate(timestamp);
    if (!date) {
      errors.push(`${fieldName} 字符串无法转换为有效日期: ${timestamp}`);
    } else {
      warnings.push(`${fieldName} 是字符串，应该是 Date 对象`);
    }
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  // 其他类型
  errors.push(`${fieldName} 类型错误，应该是 Date 对象或日期字符串，实际类型: ${typeof timestamp}`);
  return { isValid: false, errors, warnings };
}

/**
 * 修复时间戳字段
 */
export function repairTimestamp(timestamp: any, fieldName: string = 'timestamp'): RepairResult<Date> {
  const errors: string[] = [];
  const repaired: string[] = [];

  // 调试日志：记录原始时间戳的详细信息
  console.log(`[repairTimestamp] 处理 ${fieldName}:`, {
    value: timestamp,
    type: typeof timestamp,
    isDate: timestamp instanceof Date,
    isObject: timestamp && typeof timestamp === 'object',
    keys: timestamp && typeof timestamp === 'object' ? Object.keys(timestamp) : null,
    stringValue: String(timestamp)
  });

  // 如果已经是有效的 Date 对象
  if (timestamp instanceof Date && isValidDate(timestamp)) {
    return { success: true, data: timestamp, errors, repaired };
  }

  // 处理序列化增强器的特殊格式 {__DATE__: "2024-01-01T00:00:00.000Z"}
  if (timestamp && typeof timestamp === 'object' && timestamp.__DATE__) {
    const date = createSafeDate(timestamp.__DATE__);
    if (date) {
      repaired.push(`${fieldName} 从序列化标记对象转换为 Date 对象`);
      return { success: true, data: date, errors, repaired };
    }
  }

  // 尝试从字符串创建 Date 对象
  if (typeof timestamp === 'string') {
    const date = createSafeDate(timestamp);
    if (date) {
      repaired.push(`${fieldName} 从字符串转换为 Date 对象`);
      return { success: true, data: date, errors, repaired };
    }
  }

  // 尝试从数字创建 Date 对象
  if (typeof timestamp === 'number') {
    const date = createSafeDate(timestamp);
    if (date) {
      repaired.push(`${fieldName} 从数字转换为 Date 对象`);
      return { success: true, data: date, errors, repaired };
    }
  }

  // 如果是对象，尝试从对象的属性中提取日期
  if (timestamp && typeof timestamp === 'object') {
    // 尝试常见的日期属性名
    const dateProps = ['value', 'date', 'time', 'timestamp', 'created_at', 'iso'];
    for (const prop of dateProps) {
      if (timestamp[prop]) {
        const date = createSafeDate(timestamp[prop]);
        if (date) {
          repaired.push(`${fieldName} 从对象属性 ${prop} 转换为 Date 对象`);
          return { success: true, data: date, errors, repaired };
        }
      }
    }

    // 尝试将整个对象转换为字符串再解析
    try {
      const objString = JSON.stringify(timestamp);
      console.log(`[repairTimestamp] 尝试解析对象字符串: ${objString}`);
    } catch (e) {
      // 忽略JSON序列化错误
    }
  }

  // 无法修复
  errors.push(`无法修复 ${fieldName}，原始值类型: ${typeof timestamp}, 值: ${String(timestamp)}`);
  return { success: false, data: null, errors, repaired };
}

/**
 * 验证 FeedUser 对象
 */
export function validateFeedUser(user: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!user || typeof user !== 'object') {
    errors.push('用户对象不能为空且必须是对象');
    return { isValid: false, errors, warnings };
  }
  
  // 必需字段验证
  if (!user.id || typeof user.id !== 'string') {
    errors.push('用户ID必须是非空字符串');
  }
  
  if (!user.name || typeof user.name !== 'string') {
    errors.push('用户名必须是非空字符串');
  }
  
  // 可选字段验证
  if (user.avatar && typeof user.avatar !== 'string') {
    warnings.push('用户头像应该是字符串');
  }
  
  if (user.isVerified !== undefined && typeof user.isVerified !== 'boolean') {
    warnings.push('用户验证状态应该是布尔值');
  }
  
  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 修复 FeedUser 对象
 */
export function repairFeedUser(user: any): RepairResult<FeedUser> {
  const errors: string[] = [];
  const repaired: string[] = [];
  
  if (!user || typeof user !== 'object') {
    errors.push('用户对象无效，无法修复');
    return { success: false, data: null, errors, repaired };
  }
  
  const repairedUser: FeedUser = {
    id: '',
    name: '',
    username: '',
    avatar: '/api/placeholder/40/40'
  };
  
  // 修复 ID
  if (user.id && typeof user.id === 'string') {
    repairedUser.id = user.id;
  } else if (user.id) {
    repairedUser.id = String(user.id);
    repaired.push('用户ID转换为字符串');
  } else {
    errors.push('用户ID缺失，无法修复');
    return { success: false, data: null, errors, repaired };
  }
  
  // 修复名称
  if (user.name && typeof user.name === 'string') {
    repairedUser.name = user.name;
  } else if (user.name) {
    repairedUser.name = String(user.name);
    repaired.push('用户名转换为字符串');
  } else {
    repairedUser.name = '未知用户';
    repaired.push('用户名设置为默认值');
  }
  
  // 修复用户名
  if (user.username && typeof user.username === 'string') {
    repairedUser.username = user.username;
  } else {
    repairedUser.username = `user_${repairedUser.id}`;
    repaired.push('用户名设置为默认值');
  }
  
  // 修复头像
  if (user.avatar && typeof user.avatar === 'string') {
    repairedUser.avatar = user.avatar;
  } else {
    repaired.push('头像设置为默认值');
  }
  
  // 修复可选字段
  if (user.isVerified !== undefined) {
    repairedUser.isVerified = Boolean(user.isVerified);
    if (typeof user.isVerified !== 'boolean') {
      repaired.push('验证状态转换为布尔值');
    }
  }
  
  if (user.isFollowing !== undefined) {
    repairedUser.isFollowing = Boolean(user.isFollowing);
    if (typeof user.isFollowing !== 'boolean') {
      repaired.push('关注状态转换为布尔值');
    }
  }
  
  return { success: true, data: repairedUser, errors, repaired };
}

/**
 * 验证 FeedPostStats 对象
 */
export function validateFeedPostStats(stats: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!stats || typeof stats !== 'object') {
    errors.push('统计对象不能为空且必须是对象');
    return { isValid: false, errors, warnings };
  }
  
  // 验证数字字段
  const numberFields = ['likes', 'comments', 'shares', 'views', 'reports'];
  for (const field of numberFields) {
    if (stats[field] !== undefined && typeof stats[field] !== 'number') {
      warnings.push(`${field} 应该是数字类型`);
    }
  }
  
  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 修复 FeedPostStats 对象
 */
export function repairFeedPostStats(stats: any): RepairResult<FeedPostStats> {
  const errors: string[] = [];
  const repaired: string[] = [];
  
  if (!stats || typeof stats !== 'object') {
    // 创建默认统计对象
    const defaultStats: FeedPostStats = {
      likes: 0,
      comments: 0,
      shares: 0,
      views: 0,
      reports: 0
    };
    repaired.push('创建默认统计对象');
    return { success: true, data: defaultStats, errors, repaired };
  }
  
  const repairedStats: FeedPostStats = {
    likes: 0,
    comments: 0,
    shares: 0
  };
  
  // 修复必需字段
  const requiredFields: (keyof FeedPostStats)[] = ['likes', 'comments', 'shares'];
  for (const field of requiredFields) {
    if (typeof stats[field] === 'number') {
      repairedStats[field] = Math.max(0, stats[field]);
    } else if (stats[field] !== undefined) {
      const num = Number(stats[field]);
      if (!isNaN(num)) {
        repairedStats[field] = Math.max(0, num);
        repaired.push(`${field} 转换为数字`);
      } else {
        repaired.push(`${field} 设置为默认值 0`);
      }
    }
  }
  
  // 修复可选字段
  if (stats.views !== undefined) {
    if (typeof stats.views === 'number') {
      repairedStats.views = Math.max(0, stats.views);
    } else {
      const num = Number(stats.views);
      if (!isNaN(num)) {
        repairedStats.views = Math.max(0, num);
        repaired.push('views 转换为数字');
      }
    }
  }
  
  if (stats.reports !== undefined) {
    if (typeof stats.reports === 'number') {
      repairedStats.reports = Math.max(0, stats.reports);
    } else {
      const num = Number(stats.reports);
      if (!isNaN(num)) {
        repairedStats.reports = Math.max(0, num);
        repaired.push('reports 转换为数字');
      }
    }
  }
  
  return { success: true, data: repairedStats, errors, repaired };
}

/**
 * 验证完整的 FeedPost 对象
 */
export function validateFeedPost(post: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!post || typeof post !== 'object') {
    errors.push('帖子对象不能为空且必须是对象');
    return { isValid: false, errors, warnings };
  }
  
  // 验证基本字段
  if (!post.id || typeof post.id !== 'string') {
    errors.push('帖子ID必须是非空字符串');
  }
  
  // 验证时间戳
  const timestampValidation = validateTimestamp(post.timestamp);
  errors.push(...timestampValidation.errors);
  warnings.push(...timestampValidation.warnings);
  
  // 验证用户
  const userValidation = validateFeedUser(post.user);
  errors.push(...userValidation.errors);
  warnings.push(...userValidation.warnings);
  
  // 验证统计
  const statsValidation = validateFeedPostStats(post.stats);
  errors.push(...statsValidation.errors);
  warnings.push(...statsValidation.warnings);
  
  // 验证内容
  if (!post.content || typeof post.content !== 'object') {
    errors.push('帖子内容必须是对象');
  }
  
  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 修复完整的 FeedPost 对象
 */
export function repairFeedPost(post: any): RepairResult<FeedPost> {
  const errors: string[] = [];
  const repaired: string[] = [];
  
  if (!post || typeof post !== 'object') {
    errors.push('帖子对象无效，无法修复');
    return { success: false, data: null, errors, repaired };
  }
  
  // 修复基本字段
  if (!post.id || typeof post.id !== 'string') {
    errors.push('帖子ID缺失或无效，无法修复');
    return { success: false, data: null, errors, repaired };
  }
  
  // 修复时间戳
  const timestampRepair = repairTimestamp(post.timestamp);
  if (!timestampRepair.success) {
    errors.push(...timestampRepair.errors);
    return { success: false, data: null, errors, repaired };
  }
  repaired.push(...timestampRepair.repaired);
  
  // 修复用户
  const userRepair = repairFeedUser(post.user);
  if (!userRepair.success) {
    errors.push(...userRepair.errors);
    return { success: false, data: null, errors, repaired };
  }
  repaired.push(...userRepair.repaired);
  
  // 修复统计
  const statsRepair = repairFeedPostStats(post.stats);
  if (!statsRepair.success) {
    errors.push(...statsRepair.errors);
    return { success: false, data: null, errors, repaired };
  }
  repaired.push(...statsRepair.repaired);
  
  // 构建修复后的帖子对象
  const repairedPost: FeedPost = {
    id: post.id,
    timestamp: timestampRepair.data!,
    user: userRepair.data!,
    stats: statsRepair.data!,
    content: post.content || {},
    isLiked: Boolean(post.isLiked),
    visibility: post.visibility || 'everyone',
    tags: Array.isArray(post.tags) ? post.tags : []
  };
  
  // 修复可选字段
  if (post.title) {
    repairedPost.title = String(post.title);
  }
  
  if (post.location) {
    repairedPost.location = String(post.location);
  }
  
  if (post.status) {
    repairedPost.status = post.status;
  }
  
  if (Array.isArray(post.images)) {
    repairedPost.images = post.images.filter(img => typeof img === 'string');
  }
  
  return { success: true, data: repairedPost, errors, repaired };
}

/**
 * 批量修复 FeedPost 数组
 */
export function repairFeedPosts(posts: any[]): RepairResult<FeedPost[]> {
  const errors: string[] = [];
  const repaired: string[] = [];
  const repairedPosts: FeedPost[] = [];
  
  if (!Array.isArray(posts)) {
    errors.push('输入不是数组');
    return { success: false, data: null, errors, repaired };
  }
  
  posts.forEach((post, index) => {
    const repair = repairFeedPost(post);
    if (repair.success && repair.data) {
      repairedPosts.push(repair.data);
      if (repair.repaired.length > 0) {
        repaired.push(`帖子 ${index}: ${repair.repaired.join(', ')}`);
      }
    } else {
      errors.push(`帖子 ${index}: ${repair.errors.join(', ')}`);
    }
  });
  
  return {
    success: repairedPosts.length > 0,
    data: repairedPosts,
    errors,
    repaired
  };
}
