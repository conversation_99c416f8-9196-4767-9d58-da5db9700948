/**
 * 缓存序列化增强器
 * 解决 Date 对象在缓存中的序列化/反序列化问题
 * 
 * @fileoverview 缓存序列化增强工具
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * 日期字段标识符
 * 用于在序列化时标记 Date 对象
 */
const DATE_MARKER = '__DATE__';

/**
 * 需要处理的日期字段名称
 * 可以根据需要扩展
 */
const DATE_FIELDS = [
  'timestamp',
  'created_at',
  'updated_at',
  'lastAccessed',
  'expires_at',
  'start_time',
  'end_time'
];

/**
 * 检查值是否为 Date 对象
 */
function isDate(value: any): value is Date {
  return value instanceof Date && !isNaN(value.getTime());
}

/**
 * 检查字段名是否为日期字段
 */
function isDateField(key: string): boolean {
  return DATE_FIELDS.includes(key) || key.endsWith('_at') || key.endsWith('Time');
}

/**
 * 序列化替换器
 * 将 Date 对象转换为特殊标记的字符串
 */
function serializeReplacer(key: string, value: any): any {
  // 如果是 Date 对象，转换为标记字符串
  if (isDate(value)) {
    return {
      [DATE_MARKER]: value.toISOString()
    };
  }
  
  // 如果是字符串但字段名表明它应该是日期，尝试验证
  if (typeof value === 'string' && isDateField(key)) {
    try {
      const date = new Date(value);
      if (isDate(date)) {
        return {
          [DATE_MARKER]: date.toISOString()
        };
      }
    } catch (error) {
      console.warn(`[CacheSerializationEnhancer] 无效的日期字符串: ${key}=${value}`);
    }
  }
  
  return value;
}

/**
 * 反序列化恢复器
 * 将标记的字符串转换回 Date 对象
 */
function deserializeReviver(key: string, value: any): any {
  // 调试日志：记录反序列化过程
  if (isDateField(key)) {
    console.log(`[CacheSerializationEnhancer] 处理日期字段 ${key}:`, {
      value,
      type: typeof value,
      isObject: value && typeof value === 'object',
      hasDateMarker: value && typeof value === 'object' && value[DATE_MARKER]
    });
  }

  // 检查是否为日期标记对象
  if (value && typeof value === 'object' && value[DATE_MARKER]) {
    try {
      const date = new Date(value[DATE_MARKER]);
      if (isDate(date)) {
        console.log(`[CacheSerializationEnhancer] 成功恢复日期: ${key}=${value[DATE_MARKER]}`);
        return date;
      }
    } catch (error) {
      console.warn(`[CacheSerializationEnhancer] 恢复日期失败: ${key}=${value[DATE_MARKER]}`, error);
    }
  }

  // 兼容性处理：如果是日期字段的字符串，尝试转换
  if (typeof value === 'string' && isDateField(key)) {
    try {
      const date = new Date(value);
      if (isDate(date)) {
        console.log(`[CacheSerializationEnhancer] 兼容性转换日期: ${key}=${value}`);
        return date;
      }
    } catch (error) {
      // 静默处理，保持原值
    }
  }

  return value;
}

/**
 * 增强的序列化函数
 * 支持 Date 对象的正确序列化
 */
export function enhancedStringify(data: any): string {
  try {
    return JSON.stringify(data, serializeReplacer);
  } catch (error) {
    console.error('[CacheSerializationEnhancer] 序列化失败:', error);
    throw new Error(`序列化失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 增强的反序列化函数
 * 支持 Date 对象的正确恢复
 */
export function enhancedParse<T = any>(jsonString: string): T {
  try {
    return JSON.parse(jsonString, deserializeReviver) as T;
  } catch (error) {
    console.error('[CacheSerializationEnhancer] 反序列化失败:', error);
    throw new Error(`反序列化失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 批量处理数据中的日期字段
 * 确保所有日期字段都是正确的 Date 对象
 */
export function sanitizeDateFields<T extends Record<string, any>>(data: T): T {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sanitized = { ...data };

  for (const [key, value] of Object.entries(sanitized)) {
    if (isDateField(key)) {
      // 处理日期字段
      if (typeof value === 'string') {
        try {
          const date = new Date(value);
          if (isDate(date)) {
            (sanitized as any)[key] = date;
            console.log(`[sanitizeDateFields] 转换字符串日期: ${key}=${value}`);
          }
        } catch (error) {
          console.warn(`[CacheSerializationEnhancer] 无法转换日期字段 ${key}:`, value);
        }
      } else if (value && typeof value === 'object' && value[DATE_MARKER]) {
        // 处理序列化标记对象
        try {
          const date = new Date(value[DATE_MARKER]);
          if (isDate(date)) {
            (sanitized as any)[key] = date;
            console.log(`[sanitizeDateFields] 转换标记日期: ${key}=${value[DATE_MARKER]}`);
          }
        } catch (error) {
          console.warn(`[CacheSerializationEnhancer] 无法转换标记日期 ${key}:`, value);
        }
      } else if (value && typeof value === 'object') {
        // 处理其他对象格式的日期
        console.warn(`[sanitizeDateFields] 发现未知日期对象格式: ${key}=`, value);

        // 尝试从对象属性中提取日期
        const dateProps = ['value', 'date', 'time', 'timestamp', 'iso', '__DATE__'];
        for (const prop of dateProps) {
          if (value[prop]) {
            try {
              const date = new Date(value[prop]);
              if (isDate(date)) {
                (sanitized as any)[key] = date;
                console.log(`[sanitizeDateFields] 从对象属性 ${prop} 转换日期: ${key}=${value[prop]}`);
                break;
              }
            } catch (error) {
              // 继续尝试下一个属性
            }
          }
        }
      }
    } else if (value && typeof value === 'object' && !Array.isArray(value)) {
      // 递归处理嵌套对象
      (sanitized as any)[key] = sanitizeDateFields(value);
    } else if (Array.isArray(value)) {
      // 处理数组中的对象
      (sanitized as any)[key] = value.map(item =>
        item && typeof item === 'object' ? sanitizeDateFields(item) : item
      );
    }
  }

  return sanitized;
}

/**
 * 验证数据中的日期字段
 * 返回验证结果和错误信息
 */
export function validateDateFields(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!data || typeof data !== 'object') {
    return { isValid: true, errors };
  }
  
  function validateObject(obj: any, path: string = ''): void {
    for (const [key, value] of Object.entries(obj)) {
      const fullPath = path ? `${path}.${key}` : key;
      
      if (isDateField(key)) {
        if (typeof value === 'string') {
          try {
            const date = new Date(value);
            if (!isDate(date)) {
              errors.push(`无效的日期字符串: ${fullPath}=${value}`);
            }
          } catch (error) {
            errors.push(`日期解析失败: ${fullPath}=${value}`);
          }
        } else if (value !== null && value !== undefined && !isDate(value)) {
          errors.push(`日期字段类型错误: ${fullPath} 应该是 Date 或日期字符串`);
        }
      } else if (value && typeof value === 'object' && !Array.isArray(value)) {
        validateObject(value, fullPath);
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (item && typeof item === 'object') {
            validateObject(item, `${fullPath}[${index}]`);
          }
        });
      }
    }
  }
  
  validateObject(data);
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 缓存序列化增强器配置
 */
export interface CacheSerializationConfig {
  /** 是否启用增强序列化 */
  enabled: boolean;
  /** 是否启用兼容性模式 */
  compatibilityMode: boolean;
  /** 是否记录详细日志 */
  verbose: boolean;
}

/**
 * 默认配置
 */
export const DEFAULT_SERIALIZATION_CONFIG: CacheSerializationConfig = {
  enabled: true,
  compatibilityMode: true,
  verbose: false
};

/**
 * 缓存序列化增强器类
 */
export class CacheSerializationEnhancer {
  private config: CacheSerializationConfig;
  
  constructor(config: Partial<CacheSerializationConfig> = {}) {
    this.config = { ...DEFAULT_SERIALIZATION_CONFIG, ...config };
  }
  
  /**
   * 序列化数据
   */
  stringify(data: any): string {
    if (!this.config.enabled) {
      return JSON.stringify(data);
    }
    
    if (this.config.verbose) {
      console.log('[CacheSerializationEnhancer] 开始序列化数据');
    }
    
    return enhancedStringify(data);
  }
  
  /**
   * 反序列化数据
   */
  parse<T = any>(jsonString: string): T {
    if (!this.config.enabled) {
      return JSON.parse(jsonString) as T;
    }
    
    if (this.config.verbose) {
      console.log('[CacheSerializationEnhancer] 开始反序列化数据');
    }
    
    const parsed = enhancedParse<T>(jsonString);
    
    // 如果启用兼容性模式，额外处理日期字段
    if (this.config.compatibilityMode) {
      return sanitizeDateFields(parsed as any) as T;
    }
    
    return parsed;
  }
  
  /**
   * 验证数据
   */
  validate(data: any): { isValid: boolean; errors: string[] } {
    return validateDateFields(data);
  }
}

/**
 * 默认的缓存序列化增强器实例
 */
export const cacheSerializationEnhancer = new CacheSerializationEnhancer();
