/**
 * Feed 错误边界组件
 * 捕获和处理 Feed 相关的运行时错误
 * 
 * @fileoverview Feed 错误边界组件
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';

/**
 * 错误边界状态接口
 */
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * 错误边界属性接口
 */
interface FeedErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  className?: string;
}

/**
 * Feed 错误边界组件
 * 专门处理 Feed 页面的错误情况
 */
export class FeedErrorBoundary extends Component<FeedErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: FeedErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }
  
  /**
   * 捕获错误并更新状态
   */
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `feed_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }
  
  /**
   * 处理错误信息
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      errorInfo
    });
    
    // 记录错误日志
    console.error('[FeedErrorBoundary] 捕获到错误:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString()
    });
    
    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // 发送错误报告（可选）
    this.reportError(error, errorInfo);
  }
  
  /**
   * 重置错误状态
   */
  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };
  
  /**
   * 刷新页面
   */
  refreshPage = () => {
    window.location.reload();
  };
  
  /**
   * 报告错误（可扩展到错误监控服务）
   */
  private reportError(error: Error, errorInfo: ErrorInfo) {
    try {
      // 这里可以集成错误监控服务，如 Sentry
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        errorId: this.state.errorId
      };
      
      // 暂时只记录到控制台，生产环境可以发送到监控服务
      console.error('[FeedErrorBoundary] 错误报告:', errorReport);
      
    } catch (reportError) {
      console.error('[FeedErrorBoundary] 发送错误报告失败:', reportError);
    }
  }
  
  /**
   * 渲染错误界面
   */
  renderErrorUI() {
    const { error, errorInfo, errorId } = this.state;
    const { showDetails = false } = this.props;
    
    return (
      <div className={`feed-error-boundary ${this.props.className || ''}`}>
        <div className="error-container">
          {/* 错误图标 */}
          <div className="error-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
          </div>
          
          {/* 错误标题 */}
          <h2 className="error-title">动态加载出现问题</h2>
          
          {/* 错误描述 */}
          <p className="error-description">
            很抱歉，动态内容加载时遇到了问题。请尝试刷新页面或稍后再试。
          </p>
          
          {/* 操作按钮 */}
          <div className="error-actions">
            <button 
              className="btn btn-primary"
              onClick={this.resetError}
            >
              重试
            </button>
            <button 
              className="btn btn-secondary"
              onClick={this.refreshPage}
            >
              刷新页面
            </button>
          </div>
          
          {/* 错误详情（开发模式） */}
          {showDetails && error && (
            <details className="error-details">
              <summary>错误详情 (ID: {errorId})</summary>
              <div className="error-content">
                <h4>错误信息:</h4>
                <pre className="error-message">{error.message}</pre>
                
                {error.stack && (
                  <>
                    <h4>错误堆栈:</h4>
                    <pre className="error-stack">{error.stack}</pre>
                  </>
                )}
                
                {errorInfo?.componentStack && (
                  <>
                    <h4>组件堆栈:</h4>
                    <pre className="error-component-stack">{errorInfo.componentStack}</pre>
                  </>
                )}
              </div>
            </details>
          )}
        </div>
        
        {/* 样式 */}
        <style jsx>{`
          .feed-error-boundary {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            padding: var(--space-6);
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            margin: var(--space-4);
          }
          
          .error-container {
            text-align: center;
            max-width: 500px;
          }
          
          .error-icon {
            color: var(--color-error);
            margin-bottom: var(--space-4);
          }
          
          .error-title {
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-2);
          }
          
          .error-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-6);
            line-height: 1.6;
          }
          
          .error-actions {
            display: flex;
            gap: var(--space-3);
            justify-content: center;
            margin-bottom: var(--space-6);
          }
          
          .btn {
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-md);
            border: none;
            font-weight: var(--font-medium);
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: var(--ios-touch-target);
          }
          
          .btn-primary {
            background: var(--color-primary);
            color: white;
          }
          
          .btn-primary:hover {
            background: var(--color-primary-dark);
            transform: translateY(-1px);
          }
          
          .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
          }
          
          .btn-secondary:hover {
            background: var(--bg-secondary);
            transform: translateY(-1px);
          }
          
          .error-details {
            text-align: left;
            margin-top: var(--space-4);
            padding: var(--space-4);
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
          }
          
          .error-details summary {
            cursor: pointer;
            font-weight: var(--font-medium);
            margin-bottom: var(--space-2);
          }
          
          .error-content h4 {
            margin: var(--space-3) 0 var(--space-1) 0;
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-secondary);
          }
          
          .error-message,
          .error-stack,
          .error-component-stack {
            background: var(--bg-secondary);
            padding: var(--space-2);
            border-radius: var(--radius-sm);
            font-size: var(--text-xs);
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-word;
          }
          
          @media (max-width: 768px) {
            .feed-error-boundary {
              min-height: 300px;
              padding: var(--space-4);
              margin: var(--space-2);
            }
            
            .error-actions {
              flex-direction: column;
              align-items: center;
            }
            
            .btn {
              width: 100%;
              max-width: 200px;
            }
          }
        `}</style>
      </div>
    );
  }
  
  render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // 否则使用默认错误界面
      return this.renderErrorUI();
    }
    
    return this.props.children;
  }
}

/**
 * 函数式错误边界 Hook（React 18+）
 * 用于函数组件中的错误处理
 */
export function useFeedErrorHandler() {
  const handleError = React.useCallback((error: Error, errorInfo?: ErrorInfo) => {
    console.error('[FeedErrorHandler] 处理错误:', {
      error: error.message,
      stack: error.stack,
      errorInfo,
      timestamp: new Date().toISOString()
    });
    
    // 这里可以添加错误上报逻辑
  }, []);
  
  return { handleError };
}

/**
 * 默认导出
 */
export default FeedErrorBoundary;
